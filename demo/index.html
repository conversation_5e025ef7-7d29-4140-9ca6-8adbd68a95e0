<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票交易系统 - Demo</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome CDN -->
    <link href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        /* 自定义样式 */
        .price-up { color: #ef4444; }
        .price-down { color: #22c55e; }
        .price-flat { color: #6b7280; }
        .profit { color: #ef4444; }
        .loss { color: #22c55e; }
        .stock-code { font-family: 'Monaco', 'Consolas', monospace; font-weight: bold; }
        .amount-display { font-family: 'Monaco', 'Consolas', monospace; }
        .big-number { font-size: 1.5rem; font-weight: bold; }
        
        /* 表格样式 */
        .trading-table th { background-color: #f8fafc; }
        .trading-table tr:hover { background-color: #f1f5f9; }
        
        /* 按钮样式 */
        .buy-btn { background-color: #ef4444; border-color: #ef4444; }
        .buy-btn:hover { background-color: #dc2626; }
        .sell-btn { background-color: #22c55e; border-color: #22c55e; }
        .sell-btn:hover { background-color: #16a34a; }
        
        /* 深度行情样式 */
        .depth-row { display: flex; justify-content: space-between; padding: 2px 8px; }
        .sell-row { background-color: #fef2f2; }
        .buy-row { background-color: #f0fdf4; }
        
        /* 隐藏类 */
        .hidden { display: none; }
        
        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            z-index: 1001;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 顶部导航栏 -->
    <nav class="bg-blue-600 text-white p-4 shadow-lg">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <i class="fas fa-chart-line text-2xl"></i>
                <h1 class="text-xl font-bold">股票交易系统</h1>
                <span class="text-sm bg-green-500 px-2 py-1 rounded">在线</span>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm">当前时间: <span id="currentTime"></span></span>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-user"></i>
                    <span>演示账户</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container mx-auto p-4">
        <!-- 标签页导航 -->
        <div class="bg-white rounded-lg shadow-md mb-4">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6">
                    <button class="tab-btn py-4 px-2 border-b-2 border-blue-500 text-blue-600 font-medium" data-tab="stockPool">
                        <i class="fas fa-list mr-2"></i>股票池
                    </button>
                    <button class="tab-btn py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="trading">
                        <i class="fas fa-exchange-alt mr-2"></i>交易下单
                    </button>
                    <button class="tab-btn py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="position">
                        <i class="fas fa-wallet mr-2"></i>持仓管理
                    </button>
                </nav>
            </div>
        </div>

        <!-- 股票池模块 -->
        <div id="stockPool" class="tab-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <!-- 工具栏 -->
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center space-x-4">
                        <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600" onclick="showAddStockModal()">
                            <i class="fas fa-plus mr-2"></i>添加股票
                        </button>
                        <input type="text" id="stockSearch" placeholder="搜索股票代码或名称" 
                               class="border border-gray-300 rounded px-3 py-2 w-64" onkeyup="filterStocks()">
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">排序:</span>
                        <select id="sortBy" class="border border-gray-300 rounded px-3 py-2" onchange="sortStocks()">
                            <option value="code">代码</option>
                            <option value="change">涨跌幅</option>
                            <option value="volume">成交量</option>
                        </select>
                        <button class="bg-gray-500 text-white px-3 py-2 rounded hover:bg-gray-600" onclick="refreshStockPool()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>

                <!-- 股票列表表格 -->
                <div class="overflow-x-auto">
                    <table class="w-full trading-table">
                        <thead>
                            <tr class="text-left">
                                <th class="px-4 py-3">代码</th>
                                <th class="px-4 py-3">名称</th>
                                <th class="px-4 py-3 text-right">现价</th>
                                <th class="px-4 py-3 text-right">涨跌额</th>
                                <th class="px-4 py-3 text-right">涨跌幅</th>
                                <th class="px-4 py-3 text-right">成交量</th>
                                <th class="px-4 py-3 text-right">成交额</th>
                                <th class="px-4 py-3 text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody id="stockPoolTable">
                            <!-- 股票数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 交易下单模块 -->
        <div id="trading" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧：行情信息 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold mb-4">行情信息</h3>
                    
                    <!-- 股票选择 -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择股票</label>
                        <select id="tradingStockSelect" class="w-full border border-gray-300 rounded px-3 py-2" onchange="selectTradingStock()">
                            <option value="">请选择股票</option>
                        </select>
                    </div>

                    <!-- 股票基本信息 -->
                    <div id="stockInfo" class="mb-6 hidden">
                        <div class="border-b pb-4 mb-4">
                            <h4 id="stockName" class="text-lg font-semibold"></h4>
                            <div class="flex items-center space-x-4 mt-2">
                                <span id="stockPrice" class="text-2xl font-bold"></span>
                                <span id="stockChange" class="text-sm"></span>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>开盘: <span id="openPrice"></span></div>
                            <div>昨收: <span id="prevClose"></span></div>
                            <div>最高: <span id="highPrice" class="price-up"></span></div>
                            <div>最低: <span id="lowPrice" class="price-down"></span></div>
                        </div>
                    </div>

                    <!-- 五档行情 -->
                    <div id="depthInfo" class="hidden">
                        <h5 class="font-medium mb-2">五档行情</h5>
                        <div class="text-xs">
                            <div id="sellDepth" class="mb-2">
                                <!-- 卖档数据 -->
                            </div>
                            <div id="buyDepth">
                                <!-- 买档数据 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中间：交易面板 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold mb-4">交易下单</h3>
                    
                    <!-- 买卖方向 -->
                    <div class="flex mb-4">
                        <button id="buyTab" class="flex-1 py-2 px-4 bg-red-500 text-white rounded-l buy-btn" onclick="setTradeDirection('buy')">
                            买入
                        </button>
                        <button id="sellTab" class="flex-1 py-2 px-4 bg-gray-300 text-gray-700 rounded-r" onclick="setTradeDirection('sell')">
                            卖出
                        </button>
                    </div>

                    <!-- 交易表单 -->
                    <form id="orderForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">订单类型</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="orderType" value="limit" checked class="mr-2">
                                    限价单
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="orderType" value="market" class="mr-2">
                                    市价单
                                </label>
                            </div>
                        </div>

                        <div id="priceField">
                            <label class="block text-sm font-medium text-gray-700 mb-1">委托价格</label>
                            <input type="number" id="orderPrice" step="0.01" min="0" 
                                   class="w-full border border-gray-300 rounded px-3 py-2" 
                                   placeholder="0.00" onchange="calculateAmount()">
                            <div class="flex space-x-2 mt-2">
                                <button type="button" class="text-xs bg-gray-200 px-2 py-1 rounded" onclick="setPriceFromDepth('buy')">买一</button>
                                <button type="button" class="text-xs bg-gray-200 px-2 py-1 rounded" onclick="setPriceFromDepth('sell')">卖一</button>
                                <button type="button" class="text-xs bg-gray-200 px-2 py-1 rounded" onclick="setPriceFromCurrent()">现价</button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">委托数量</label>
                            <input type="number" id="orderQuantity" step="100" min="100" 
                                   class="w-full border border-gray-300 rounded px-3 py-2" 
                                   placeholder="100" onchange="calculateAmount()">
                            <div class="flex space-x-2 mt-2">
                                <button type="button" class="text-xs bg-gray-200 px-2 py-1 rounded" onclick="setQuantity(1000)">1000</button>
                                <button type="button" class="text-xs bg-gray-200 px-2 py-1 rounded" onclick="setQuantity(2000)">2000</button>
                                <button type="button" class="text-xs bg-gray-200 px-2 py-1 rounded" onclick="setQuantity(5000)">5000</button>
                                <button type="button" class="text-xs bg-gray-200 px-2 py-1 rounded" onclick="setMaxQuantity()">最大</button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">委托金额</label>
                            <div class="amount-display text-lg font-semibold" id="orderAmount">0.00 元</div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1" id="availableLabel">可用资金</label>
                            <div class="text-lg font-semibold" id="availableAmount">1,000,000.00 元</div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">交易密码</label>
                            <input type="password" id="tradePassword" 
                                   class="w-full border border-gray-300 rounded px-3 py-2" 
                                   placeholder="请输入交易密码">
                        </div>

                        <button type="button" id="submitOrderBtn" 
                                class="w-full py-3 px-4 bg-red-500 text-white rounded font-semibold buy-btn" 
                                onclick="submitOrder()">
                            买入
                        </button>
                    </form>
                </div>

                <!-- 右侧：账户和委托信息 -->
                <div class="space-y-6">
                    <!-- 账户资金 -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold mb-4">账户资金</h3>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>总资产: <span class="font-semibold amount-display" id="totalAssets">1,500,000.00</span></div>
                            <div>可用资金: <span class="font-semibold amount-display" id="availableFunds">1,000,000.00</span></div>
                            <div>持仓市值: <span class="font-semibold amount-display" id="positionValue">500,000.00</span></div>
                            <div>今日盈亏: <span class="font-semibold profit" id="todayPnl">+5,000.00</span></div>
                        </div>
                    </div>

                    <!-- 当日委托 -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold mb-4">当日委托</h3>
                        <div id="todayOrders" class="space-y-2 text-sm">
                            <!-- 委托数据将通过JavaScript动态填充 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 持仓管理模块 -->
        <div id="position" class="tab-content hidden">
            <!-- 持仓概况 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="text-sm text-gray-600">总市值</div>
                    <div class="big-number amount-display" id="totalMarketValue">500,000.00</div>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="text-sm text-gray-600">持仓成本</div>
                    <div class="big-number amount-display" id="totalCost">495,000.00</div>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="text-sm text-gray-600">持仓盈亏</div>
                    <div class="big-number profit amount-display" id="totalPnl">+5,000.00</div>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="text-sm text-gray-600">收益率</div>
                    <div class="big-number profit" id="totalReturnRate">+1.01%</div>
                </div>
            </div>

            <!-- 持仓列表 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">持仓明细</h3>
                    <div class="flex items-center space-x-4">
                        <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600" onclick="refreshPositions()">
                            <i class="fas fa-sync-alt mr-2"></i>刷新
                        </button>
                        <input type="text" id="positionSearch" placeholder="搜索股票" 
                               class="border border-gray-300 rounded px-3 py-2 w-48" onkeyup="filterPositions()">
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full trading-table">
                        <thead>
                            <tr class="text-left">
                                <th class="px-4 py-3">代码</th>
                                <th class="px-4 py-3">名称</th>
                                <th class="px-4 py-3 text-right">持仓股数</th>
                                <th class="px-4 py-3 text-right">可用股数</th>
                                <th class="px-4 py-3 text-right">成本价</th>
                                <th class="px-4 py-3 text-right">现价</th>
                                <th class="px-4 py-3 text-right">市值</th>
                                <th class="px-4 py-3 text-right">盈亏金额</th>
                                <th class="px-4 py-3 text-right">收益率</th>
                                <th class="px-4 py-3 text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody id="positionTable">
                            <!-- 持仓数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加股票模态框 -->
    <div id="addStockModal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">添加股票到股票池</h3>
                <button onclick="hideAddStockModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addStockForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">股票代码</label>
                    <input type="text" id="newStockCode" class="w-full border border-gray-300 rounded px-3 py-2" 
                           placeholder="例如: 000001" maxlength="6">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">股票名称</label>
                    <input type="text" id="newStockName" class="w-full border border-gray-300 rounded px-3 py-2" 
                           placeholder="例如: 平安银行">
                </div>
                <div class="flex space-x-4">
                    <button type="button" onclick="hideAddStockModal()" 
                            class="flex-1 py-2 px-4 border border-gray-300 rounded text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" onclick="addStock()" 
                            class="flex-1 py-2 px-4 bg-blue-500 text-white rounded hover:bg-blue-600">
                        添加
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 交易确认模态框 -->
    <div id="confirmModal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">交易确认</h3>
                <button onclick="hideConfirmModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="confirmContent" class="space-y-4">
                <!-- 确认内容将通过JavaScript动态填充 -->
            </div>
            <div class="flex space-x-4 mt-6">
                <button type="button" onclick="hideConfirmModal()" 
                        class="flex-1 py-2 px-4 border border-gray-300 rounded text-gray-700 hover:bg-gray-50">
                    取消
                </button>
                <button type="button" onclick="confirmTrade()" 
                        class="flex-1 py-2 px-4 bg-blue-500 text-white rounded hover:bg-blue-600">
                    确认交易
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'stockPool';
        let tradeDirection = 'buy';
        let selectedTradingStock = null;
        let stockPoolData = [];
        let positionData = [];
        let orderData = [];
        let accountData = {
            totalAssets: 1500000,
            availableFunds: 1000000,
            positionValue: 500000,
            todayPnl: 5000
        };

        // 初始化数据
        function initializeData() {
            // 初始化股票池数据
            stockPoolData = [
                {
                    code: '000001',
                    name: '平安银行',
                    currentPrice: 12.85,
                    openPrice: 12.90,
                    highPrice: 13.10,
                    lowPrice: 12.80,
                    prevClose: 12.88,
                    changeAmount: -0.03,
                    changePercent: -0.23,
                    volume: ********,
                    turnover: *********
                },
                {
                    code: '000002',
                    name: '万科A',
                    currentPrice: 18.45,
                    openPrice: 18.20,
                    highPrice: 18.68,
                    lowPrice: 18.15,
                    prevClose: 18.30,
                    changeAmount: 0.15,
                    changePercent: 0.82,
                    volume: 8950000,
                    turnover: 164820000
                },
                {
                    code: '600036',
                    name: '招商银行',
                    currentPrice: 35.68,
                    openPrice: 35.20,
                    highPrice: 36.15,
                    lowPrice: 35.10,
                    prevClose: 35.45,
                    changeAmount: 0.23,
                    changePercent: 0.65,
                    volume: 12350000,
                    turnover: 441230000
                },
                {
                    code: '600519',
                    name: '贵州茅台',
                    currentPrice: 1685.50,
                    openPrice: 1680.00,
                    highPrice: 1695.80,
                    lowPrice: 1675.20,
                    prevClose: 1678.90,
                    changeAmount: 6.60,
                    changePercent: 0.39,
                    volume: 1250000,
                    turnover: 2108750000
                },
                {
                    code: '000858',
                    name: '五粮液',
                    currentPrice: 128.45,
                    openPrice: 129.80,
                    highPrice: 130.20,
                    lowPrice: 127.90,
                    prevClose: 129.60,
                    changeAmount: -1.15,
                    changePercent: -0.89,
                    volume: 3680000,
                    turnover: 473240000
                }
            ];

            // 初始化持仓数据
            positionData = [
                {
                    stockCode: '000001',
                    stockName: '平安银行',
                    quantity: 2000,
                    availableQuantity: 2000,
                    avgCost: 12.90,
                    currentPrice: 12.85,
                    marketValue: 25700,
                    costValue: 25800,
                    pnlAmount: -100,
                    returnRate: -0.39
                },
                {
                    stockCode: '600036',
                    stockName: '招商银行',
                    quantity: 1000,
                    availableQuantity: 1000,
                    avgCost: 35.20,
                    currentPrice: 35.68,
                    marketValue: 35680,
                    costValue: 35200,
                    pnlAmount: 480,
                    returnRate: 1.36
                },
                {
                    stockCode: '600519',
                    stockName: '贵州茅台',
                    quantity: 100,
                    availableQuantity: 100,
                    avgCost: 1675.00,
                    currentPrice: 1685.50,
                    marketValue: 168550,
                    costValue: 167500,
                    pnlAmount: 1050,
                    returnRate: 0.63
                }
            ];

            // 初始化委托数据
            orderData = [
                {
                    id: 'ORD001',
                    stockCode: '000002',
                    stockName: '万科A',
                    direction: 'buy',
                    price: 18.40,
                    quantity: 1000,
                    status: 'pending',
                    time: '09:30:15'
                },
                {
                    id: 'ORD002',
                    stockCode: '000858',
                    stockName: '五粮液',
                    direction: 'sell',
                    price: 129.00,
                    quantity: 500,
                    status: 'filled',
                    time: '10:15:32'
                }
            ];

            // 渲染初始数据
            renderStockPool();
            renderPositions();
            renderTodayOrders();
            updateTradingStockSelect();
            updateAccountInfo();
        }

        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const tabName = this.dataset.tab;
                switchTab(tabName);
            });
        });

        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                if (btn.dataset.tab === tabName) {
                    btn.className = 'tab-btn py-4 px-2 border-b-2 border-blue-500 text-blue-600 font-medium';
                } else {
                    btn.className = 'tab-btn py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700';
                }
            });

            // 显示对应内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            document.getElementById(tabName).classList.remove('hidden');

            currentTab = tabName;
        }

        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 格式化数字
        function formatNumber(num, decimals = 2) {
            return num.toLocaleString('zh-CN', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        }

        function formatAmount(amount) {
            return formatNumber(amount, 2);
        }

        function formatVolume(volume) {
            if (volume >= 100000000) {
                return (volume / 100000000).toFixed(2) + '亿';
            } else if (volume >= 10000) {
                return (volume / 10000).toFixed(0) + '万';
            }
            return volume.toString();
        }

        // 获取价格样式类
        function getPriceClass(changePercent) {
            if (changePercent > 0) return 'price-up';
            if (changePercent < 0) return 'price-down';
            return 'price-flat';
        }

        // 股票池相关函数
        function renderStockPool() {
            const tbody = document.getElementById('stockPoolTable');
            tbody.innerHTML = '';

            stockPoolData.forEach(stock => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                const changeClass = getPriceClass(stock.changePercent);
                const changePrefix = stock.changeAmount >= 0 ? '+' : '';

                row.innerHTML = `
                    <td class="px-4 py-3 stock-code">${stock.code}</td>
                    <td class="px-4 py-3">${stock.name}</td>
                    <td class="px-4 py-3 text-right ${changeClass}">${formatNumber(stock.currentPrice)}</td>
                    <td class="px-4 py-3 text-right ${changeClass}">${changePrefix}${formatNumber(stock.changeAmount)}</td>
                    <td class="px-4 py-3 text-right ${changeClass}">${changePrefix}${formatNumber(stock.changePercent)}%</td>
                    <td class="px-4 py-3 text-right">${formatVolume(stock.volume)}</td>
                    <td class="px-4 py-3 text-right">${formatVolume(stock.turnover)}</td>
                    <td class="px-4 py-3 text-center">
                        <button class="bg-red-500 text-white px-2 py-1 rounded text-xs mr-1 hover:bg-red-600"
                                onclick="quickBuy('${stock.code}')">买入</button>
                        <button class="bg-green-500 text-white px-2 py-1 rounded text-xs mr-1 hover:bg-green-600"
                                onclick="quickSell('${stock.code}')">卖出</button>
                        <button class="bg-blue-500 text-white px-2 py-1 rounded text-xs mr-1 hover:bg-blue-600"
                                onclick="showAnalysis('${stock.code}')">分析</button>
                        <button class="bg-gray-500 text-white px-2 py-1 rounded text-xs hover:bg-gray-600"
                                onclick="removeStock('${stock.code}')">移除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function filterStocks() {
            const searchTerm = document.getElementById('stockSearch').value.toLowerCase();
            const rows = document.querySelectorAll('#stockPoolTable tr');

            rows.forEach(row => {
                const code = row.cells[0].textContent.toLowerCase();
                const name = row.cells[1].textContent.toLowerCase();
                if (code.includes(searchTerm) || name.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function sortStocks() {
            const sortBy = document.getElementById('sortBy').value;

            stockPoolData.sort((a, b) => {
                switch(sortBy) {
                    case 'code':
                        return a.code.localeCompare(b.code);
                    case 'change':
                        return b.changePercent - a.changePercent;
                    case 'volume':
                        return b.volume - a.volume;
                    default:
                        return 0;
                }
            });

            renderStockPool();
        }

        function refreshStockPool() {
            // 模拟刷新数据
            updateRealtimeData();
            renderStockPool();
            showNotification('股票池数据已刷新', 'success');
        }

        function showAddStockModal() {
            document.getElementById('addStockModal').classList.remove('hidden');
        }

        function hideAddStockModal() {
            document.getElementById('addStockModal').classList.add('hidden');
            document.getElementById('newStockCode').value = '';
            document.getElementById('newStockName').value = '';
        }

        function addStock() {
            const code = document.getElementById('newStockCode').value.trim();
            const name = document.getElementById('newStockName').value.trim();

            if (!code || !name) {
                showNotification('请填写完整的股票信息', 'error');
                return;
            }

            // 检查是否已存在
            if (stockPoolData.find(stock => stock.code === code)) {
                showNotification('该股票已在股票池中', 'warning');
                return;
            }

            // 添加新股票（模拟数据）
            const newStock = {
                code: code,
                name: name,
                currentPrice: Math.random() * 50 + 10,
                openPrice: Math.random() * 50 + 10,
                highPrice: Math.random() * 50 + 10,
                lowPrice: Math.random() * 50 + 10,
                prevClose: Math.random() * 50 + 10,
                changeAmount: (Math.random() - 0.5) * 2,
                changePercent: (Math.random() - 0.5) * 10,
                volume: Math.floor(Math.random() * 50000000),
                turnover: Math.floor(Math.random() * 1000000000)
            };

            stockPoolData.push(newStock);
            renderStockPool();
            updateTradingStockSelect();
            hideAddStockModal();
            showNotification('股票添加成功', 'success');
        }

        function removeStock(code) {
            if (confirm('确定要从股票池中移除这只股票吗？')) {
                stockPoolData = stockPoolData.filter(stock => stock.code !== code);
                renderStockPool();
                updateTradingStockSelect();
                showNotification('股票已移除', 'success');
            }
        }

        function quickBuy(code) {
            switchTab('trading');
            document.getElementById('tradingStockSelect').value = code;
            selectTradingStock();
            setTradeDirection('buy');
        }

        function quickSell(code) {
            switchTab('trading');
            document.getElementById('tradingStockSelect').value = code;
            selectTradingStock();
            setTradeDirection('sell');
        }

        function showAnalysis(code) {
            showNotification('技术分析功能开发中...', 'info');
        }

        // 通知函数
        function showNotification(message, type = 'info') {
            // 简单的通知实现
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 交易相关函数
        function updateTradingStockSelect() {
            const select = document.getElementById('tradingStockSelect');
            select.innerHTML = '<option value="">请选择股票</option>';

            stockPoolData.forEach(stock => {
                const option = document.createElement('option');
                option.value = stock.code;
                option.textContent = `${stock.code} ${stock.name}`;
                select.appendChild(option);
            });
        }

        function selectTradingStock() {
            const stockCode = document.getElementById('tradingStockSelect').value;
            if (!stockCode) {
                document.getElementById('stockInfo').classList.add('hidden');
                document.getElementById('depthInfo').classList.add('hidden');
                selectedTradingStock = null;
                return;
            }

            selectedTradingStock = stockPoolData.find(stock => stock.code === stockCode);
            if (selectedTradingStock) {
                updateStockInfo();
                updateDepthInfo();
                document.getElementById('stockInfo').classList.remove('hidden');
                document.getElementById('depthInfo').classList.remove('hidden');
            }
        }

        function updateStockInfo() {
            if (!selectedTradingStock) return;

            const stock = selectedTradingStock;
            const changeClass = getPriceClass(stock.changePercent);
            const changePrefix = stock.changeAmount >= 0 ? '+' : '';

            document.getElementById('stockName').textContent = `${stock.name} (${stock.code})`;
            document.getElementById('stockPrice').textContent = formatNumber(stock.currentPrice);
            document.getElementById('stockPrice').className = `text-2xl font-bold ${changeClass}`;
            document.getElementById('stockChange').textContent = `${changePrefix}${formatNumber(stock.changeAmount)} (${changePrefix}${formatNumber(stock.changePercent)}%)`;
            document.getElementById('stockChange').className = `text-sm ${changeClass}`;

            document.getElementById('openPrice').textContent = formatNumber(stock.openPrice);
            document.getElementById('prevClose').textContent = formatNumber(stock.prevClose);
            document.getElementById('highPrice').textContent = formatNumber(stock.highPrice);
            document.getElementById('lowPrice').textContent = formatNumber(stock.lowPrice);
        }

        function updateDepthInfo() {
            if (!selectedTradingStock) return;

            // 模拟五档行情数据
            const currentPrice = selectedTradingStock.currentPrice;
            const sellDepth = [];
            const buyDepth = [];

            // 生成卖档数据
            for (let i = 5; i >= 1; i--) {
                sellDepth.push({
                    level: i,
                    price: currentPrice + (i * 0.01),
                    volume: Math.floor(Math.random() * 5000) + 1000
                });
            }

            // 生成买档数据
            for (let i = 1; i <= 5; i++) {
                buyDepth.push({
                    level: i,
                    price: currentPrice - (i * 0.01),
                    volume: Math.floor(Math.random() * 5000) + 1000
                });
            }

            // 渲染卖档
            const sellDepthDiv = document.getElementById('sellDepth');
            sellDepthDiv.innerHTML = '';
            sellDepth.forEach(item => {
                const div = document.createElement('div');
                div.className = 'depth-row sell-row';
                div.innerHTML = `
                    <span>卖${item.level}</span>
                    <span class="price-down">${formatNumber(item.price)}</span>
                    <span>${item.volume}</span>
                `;
                sellDepthDiv.appendChild(div);
            });

            // 渲染买档
            const buyDepthDiv = document.getElementById('buyDepth');
            buyDepthDiv.innerHTML = '';
            buyDepth.forEach(item => {
                const div = document.createElement('div');
                div.className = 'depth-row buy-row';
                div.innerHTML = `
                    <span>买${item.level}</span>
                    <span class="price-up">${formatNumber(item.price)}</span>
                    <span>${item.volume}</span>
                `;
                buyDepthDiv.appendChild(div);
            });
        }

        function setTradeDirection(direction) {
            tradeDirection = direction;

            const buyTab = document.getElementById('buyTab');
            const sellTab = document.getElementById('sellTab');
            const submitBtn = document.getElementById('submitOrderBtn');
            const availableLabel = document.getElementById('availableLabel');
            const availableAmount = document.getElementById('availableAmount');

            if (direction === 'buy') {
                buyTab.className = 'flex-1 py-2 px-4 bg-red-500 text-white rounded-l buy-btn';
                sellTab.className = 'flex-1 py-2 px-4 bg-gray-300 text-gray-700 rounded-r';
                submitBtn.textContent = '买入';
                submitBtn.className = 'w-full py-3 px-4 bg-red-500 text-white rounded font-semibold buy-btn';
                availableLabel.textContent = '可用资金';
                availableAmount.textContent = formatAmount(accountData.availableFunds) + ' 元';
            } else {
                buyTab.className = 'flex-1 py-2 px-4 bg-gray-300 text-gray-700 rounded-l';
                sellTab.className = 'flex-1 py-2 px-4 bg-green-500 text-white rounded-r sell-btn';
                submitBtn.textContent = '卖出';
                submitBtn.className = 'w-full py-3 px-4 bg-green-500 text-white rounded font-semibold sell-btn';
                availableLabel.textContent = '可卖股数';

                // 查找持仓数量
                const position = positionData.find(p => p.stockCode === selectedTradingStock?.code);
                const availableShares = position ? position.availableQuantity : 0;
                availableAmount.textContent = availableShares.toLocaleString() + ' 股';
            }
        }

        function calculateAmount() {
            const price = parseFloat(document.getElementById('orderPrice').value) || 0;
            const quantity = parseInt(document.getElementById('orderQuantity').value) || 0;
            const amount = price * quantity;

            document.getElementById('orderAmount').textContent = formatAmount(amount) + ' 元';
        }

        function setPriceFromDepth(type) {
            if (!selectedTradingStock) return;

            const currentPrice = selectedTradingStock.currentPrice;
            let price;

            if (type === 'buy') {
                price = currentPrice - 0.01; // 买一价
            } else {
                price = currentPrice + 0.01; // 卖一价
            }

            document.getElementById('orderPrice').value = price.toFixed(2);
            calculateAmount();
        }

        function setPriceFromCurrent() {
            if (!selectedTradingStock) return;

            document.getElementById('orderPrice').value = selectedTradingStock.currentPrice.toFixed(2);
            calculateAmount();
        }

        function setQuantity(qty) {
            document.getElementById('orderQuantity').value = qty;
            calculateAmount();
        }

        function setMaxQuantity() {
            if (tradeDirection === 'buy') {
                const price = parseFloat(document.getElementById('orderPrice').value) || 0;
                if (price > 0) {
                    const maxQty = Math.floor(accountData.availableFunds / price / 100) * 100;
                    document.getElementById('orderQuantity').value = maxQty;
                }
            } else {
                const position = positionData.find(p => p.stockCode === selectedTradingStock?.code);
                if (position) {
                    document.getElementById('orderQuantity').value = position.availableQuantity;
                }
            }
            calculateAmount();
        }

        function submitOrder() {
            if (!selectedTradingStock) {
                showNotification('请先选择股票', 'error');
                return;
            }

            const orderType = document.querySelector('input[name="orderType"]:checked').value;
            const price = parseFloat(document.getElementById('orderPrice').value) || 0;
            const quantity = parseInt(document.getElementById('orderQuantity').value) || 0;
            const password = document.getElementById('tradePassword').value;

            // 验证输入
            if (orderType === 'limit' && price <= 0) {
                showNotification('请输入有效的委托价格', 'error');
                return;
            }

            if (quantity <= 0 || quantity % 100 !== 0) {
                showNotification('委托数量必须是100的整数倍', 'error');
                return;
            }

            if (!password) {
                showNotification('请输入交易密码', 'error');
                return;
            }

            // 显示确认对话框
            showConfirmModal({
                stockCode: selectedTradingStock.code,
                stockName: selectedTradingStock.name,
                direction: tradeDirection,
                orderType: orderType,
                price: orderType === 'limit' ? price : selectedTradingStock.currentPrice,
                quantity: quantity,
                amount: (orderType === 'limit' ? price : selectedTradingStock.currentPrice) * quantity
            });
        }

        function showConfirmModal(orderInfo) {
            const modal = document.getElementById('confirmModal');
            const content = document.getElementById('confirmContent');

            const directionText = orderInfo.direction === 'buy' ? '买入' : '卖出';
            const directionClass = orderInfo.direction === 'buy' ? 'text-red-500' : 'text-green-500';
            const typeText = orderInfo.orderType === 'limit' ? '限价单' : '市价单';

            content.innerHTML = `
                <div class="bg-gray-50 p-4 rounded">
                    <h4 class="font-semibold mb-3">请确认以下交易信息:</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>股票: <span class="font-semibold">${orderInfo.stockCode} ${orderInfo.stockName}</span></div>
                        <div>方向: <span class="font-semibold ${directionClass}">${directionText}</span></div>
                        <div>类型: <span class="font-semibold">${typeText}</span></div>
                        <div>价格: <span class="font-semibold">${formatNumber(orderInfo.price)} 元</span></div>
                        <div>数量: <span class="font-semibold">${orderInfo.quantity.toLocaleString()} 股</span></div>
                        <div>金额: <span class="font-semibold">${formatAmount(orderInfo.amount)} 元</span></div>
                    </div>
                </div>
                <div class="bg-yellow-50 border border-yellow-200 p-3 rounded mt-4">
                    <p class="text-yellow-800 text-sm">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        风险提示：股市有风险，投资需谨慎。请在充分了解相关风险的前提下进行投资决策。
                    </p>
                </div>
            `;

            modal.classList.remove('hidden');
            window.currentOrderInfo = orderInfo;
        }

        function hideConfirmModal() {
            document.getElementById('confirmModal').classList.add('hidden');
            window.currentOrderInfo = null;
        }

        function confirmTrade() {
            if (!window.currentOrderInfo) return;

            const orderInfo = window.currentOrderInfo;

            // 生成订单ID
            const orderId = 'ORD' + Date.now().toString().slice(-6);

            // 创建订单记录
            const newOrder = {
                id: orderId,
                stockCode: orderInfo.stockCode,
                stockName: orderInfo.stockName,
                direction: orderInfo.direction,
                price: orderInfo.price,
                quantity: orderInfo.quantity,
                status: 'pending',
                time: new Date().toLocaleTimeString()
            };

            orderData.push(newOrder);

            // 模拟订单处理
            setTimeout(() => {
                // 随机决定是否成交
                const isFilled = Math.random() > 0.3;

                if (isFilled) {
                    newOrder.status = 'filled';

                    // 更新持仓和资金
                    if (orderInfo.direction === 'buy') {
                        // 买入处理
                        accountData.availableFunds -= orderInfo.amount;

                        const existingPosition = positionData.find(p => p.stockCode === orderInfo.stockCode);
                        if (existingPosition) {
                            // 更新现有持仓
                            const totalCost = existingPosition.costValue + orderInfo.amount;
                            const totalQuantity = existingPosition.quantity + orderInfo.quantity;
                            existingPosition.avgCost = totalCost / totalQuantity;
                            existingPosition.quantity = totalQuantity;
                            existingPosition.availableQuantity = totalQuantity;
                            existingPosition.costValue = totalCost;
                            existingPosition.marketValue = totalQuantity * existingPosition.currentPrice;
                            existingPosition.pnlAmount = existingPosition.marketValue - existingPosition.costValue;
                            existingPosition.returnRate = (existingPosition.pnlAmount / existingPosition.costValue) * 100;
                        } else {
                            // 新建持仓
                            positionData.push({
                                stockCode: orderInfo.stockCode,
                                stockName: orderInfo.stockName,
                                quantity: orderInfo.quantity,
                                availableQuantity: orderInfo.quantity,
                                avgCost: orderInfo.price,
                                currentPrice: orderInfo.price,
                                marketValue: orderInfo.amount,
                                costValue: orderInfo.amount,
                                pnlAmount: 0,
                                returnRate: 0
                            });
                        }
                    } else {
                        // 卖出处理
                        accountData.availableFunds += orderInfo.amount;

                        const position = positionData.find(p => p.stockCode === orderInfo.stockCode);
                        if (position) {
                            position.quantity -= orderInfo.quantity;
                            position.availableQuantity -= orderInfo.quantity;
                            position.marketValue = position.quantity * position.currentPrice;
                            position.costValue = position.quantity * position.avgCost;
                            position.pnlAmount = position.marketValue - position.costValue;
                            position.returnRate = position.costValue > 0 ? (position.pnlAmount / position.costValue) * 100 : 0;

                            // 如果全部卖出，移除持仓
                            if (position.quantity === 0) {
                                positionData = positionData.filter(p => p.stockCode !== orderInfo.stockCode);
                            }
                        }
                    }

                    showNotification('订单已成交', 'success');
                } else {
                    showNotification('订单已提交，等待成交', 'info');
                }

                renderTodayOrders();
                renderPositions();
                updateAccountInfo();
            }, 1000);

            hideConfirmModal();

            // 清空表单
            document.getElementById('orderPrice').value = '';
            document.getElementById('orderQuantity').value = '';
            document.getElementById('tradePassword').value = '';
            document.getElementById('orderAmount').textContent = '0.00 元';

            showNotification('订单提交成功', 'success');
        }

        // 持仓相关函数
        function renderPositions() {
            const tbody = document.getElementById('positionTable');
            tbody.innerHTML = '';

            positionData.forEach(position => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                const pnlClass = position.pnlAmount >= 0 ? 'profit' : 'loss';
                const pnlPrefix = position.pnlAmount >= 0 ? '+' : '';

                row.innerHTML = `
                    <td class="px-4 py-3 stock-code">${position.stockCode}</td>
                    <td class="px-4 py-3">${position.stockName}</td>
                    <td class="px-4 py-3 text-right">${position.quantity.toLocaleString()}</td>
                    <td class="px-4 py-3 text-right">${position.availableQuantity.toLocaleString()}</td>
                    <td class="px-4 py-3 text-right">${formatNumber(position.avgCost)}</td>
                    <td class="px-4 py-3 text-right">${formatNumber(position.currentPrice)}</td>
                    <td class="px-4 py-3 text-right">${formatAmount(position.marketValue)}</td>
                    <td class="px-4 py-3 text-right ${pnlClass}">${pnlPrefix}${formatAmount(position.pnlAmount)}</td>
                    <td class="px-4 py-3 text-right ${pnlClass}">${pnlPrefix}${formatNumber(position.returnRate)}%</td>
                    <td class="px-4 py-3 text-center">
                        <button class="bg-green-500 text-white px-2 py-1 rounded text-xs mr-1 hover:bg-green-600"
                                onclick="sellPosition('${position.stockCode}')">卖出</button>
                        <button class="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600"
                                onclick="addPosition('${position.stockCode}')">加仓</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updatePositionSummary();
        }

        function updatePositionSummary() {
            const totalMarketValue = positionData.reduce((sum, p) => sum + p.marketValue, 0);
            const totalCost = positionData.reduce((sum, p) => sum + p.costValue, 0);
            const totalPnl = totalMarketValue - totalCost;
            const totalReturnRate = totalCost > 0 ? (totalPnl / totalCost) * 100 : 0;

            document.getElementById('totalMarketValue').textContent = formatAmount(totalMarketValue);
            document.getElementById('totalCost').textContent = formatAmount(totalCost);
            document.getElementById('totalPnl').textContent = (totalPnl >= 0 ? '+' : '') + formatAmount(totalPnl);
            document.getElementById('totalReturnRate').textContent = (totalReturnRate >= 0 ? '+' : '') + formatNumber(totalReturnRate) + '%';

            // 更新样式
            const pnlClass = totalPnl >= 0 ? 'profit' : 'loss';
            document.getElementById('totalPnl').className = `big-number ${pnlClass} amount-display`;
            document.getElementById('totalReturnRate').className = `big-number ${pnlClass}`;

            // 更新账户信息
            accountData.positionValue = totalMarketValue;
            accountData.totalAssets = accountData.availableFunds + totalMarketValue;
        }

        function filterPositions() {
            const searchTerm = document.getElementById('positionSearch').value.toLowerCase();
            const rows = document.querySelectorAll('#positionTable tr');

            rows.forEach(row => {
                const code = row.cells[0].textContent.toLowerCase();
                const name = row.cells[1].textContent.toLowerCase();
                if (code.includes(searchTerm) || name.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function refreshPositions() {
            // 更新持仓的当前价格
            positionData.forEach(position => {
                const stock = stockPoolData.find(s => s.code === position.stockCode);
                if (stock) {
                    position.currentPrice = stock.currentPrice;
                    position.marketValue = position.quantity * position.currentPrice;
                    position.pnlAmount = position.marketValue - position.costValue;
                    position.returnRate = position.costValue > 0 ? (position.pnlAmount / position.costValue) * 100 : 0;
                }
            });

            renderPositions();
            showNotification('持仓数据已刷新', 'success');
        }

        function sellPosition(stockCode) {
            switchTab('trading');
            document.getElementById('tradingStockSelect').value = stockCode;
            selectTradingStock();
            setTradeDirection('sell');
        }

        function addPosition(stockCode) {
            switchTab('trading');
            document.getElementById('tradingStockSelect').value = stockCode;
            selectTradingStock();
            setTradeDirection('buy');
        }

        // 委托订单相关函数
        function renderTodayOrders() {
            const container = document.getElementById('todayOrders');
            container.innerHTML = '';

            if (orderData.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-center py-4">暂无委托记录</div>';
                return;
            }

            orderData.slice(-5).reverse().forEach(order => {
                const div = document.createElement('div');
                div.className = 'border-b border-gray-200 pb-2 mb-2 last:border-b-0';

                const statusText = order.status === 'pending' ? '待成交' :
                                 order.status === 'filled' ? '已成交' : '已撤销';
                const statusClass = order.status === 'pending' ? 'text-yellow-600' :
                                  order.status === 'filled' ? 'text-green-600' : 'text-red-600';
                const directionClass = order.direction === 'buy' ? 'text-red-500' : 'text-green-500';
                const directionText = order.direction === 'buy' ? '买入' : '卖出';

                div.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div>
                            <div class="font-medium">${order.stockCode} ${order.stockName}</div>
                            <div class="text-xs text-gray-600">
                                <span class="${directionClass}">${directionText}</span>
                                ${formatNumber(order.price)} × ${order.quantity.toLocaleString()}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-xs ${statusClass}">${statusText}</div>
                            <div class="text-xs text-gray-600">${order.time}</div>
                        </div>
                    </div>
                    ${order.status === 'pending' ? `
                        <button class="text-xs bg-red-500 text-white px-2 py-1 rounded mt-2 hover:bg-red-600"
                                onclick="cancelOrder('${order.id}')">撤单</button>
                    ` : ''}
                `;
                container.appendChild(div);
            });
        }

        function cancelOrder(orderId) {
            if (confirm('确定要撤销这个委托吗？')) {
                const order = orderData.find(o => o.id === orderId);
                if (order) {
                    order.status = 'cancelled';
                    renderTodayOrders();
                    showNotification('委托已撤销', 'success');
                }
            }
        }

        // 账户信息更新
        function updateAccountInfo() {
            document.getElementById('totalAssets').textContent = formatAmount(accountData.totalAssets);
            document.getElementById('availableFunds').textContent = formatAmount(accountData.availableFunds);
            document.getElementById('positionValue').textContent = formatAmount(accountData.positionValue);
            document.getElementById('todayPnl').textContent = (accountData.todayPnl >= 0 ? '+' : '') + formatAmount(accountData.todayPnl);

            const pnlClass = accountData.todayPnl >= 0 ? 'profit' : 'loss';
            document.getElementById('todayPnl').className = `font-semibold ${pnlClass}`;
        }

        // 实时数据更新
        function updateRealtimeData() {
            // 模拟股价波动
            stockPoolData.forEach(stock => {
                const changeRate = (Math.random() - 0.5) * 0.02; // ±1%的随机波动
                const newPrice = stock.currentPrice * (1 + changeRate);

                stock.currentPrice = Math.max(0.01, newPrice);
                stock.changeAmount = stock.currentPrice - stock.prevClose;
                stock.changePercent = (stock.changeAmount / stock.prevClose) * 100;

                // 更新成交量（模拟增长）
                stock.volume += Math.floor(Math.random() * 100000);
                stock.turnover = stock.volume * stock.currentPrice;
            });

            // 更新持仓的当前价格和盈亏
            positionData.forEach(position => {
                const stock = stockPoolData.find(s => s.code === position.stockCode);
                if (stock) {
                    position.currentPrice = stock.currentPrice;
                    position.marketValue = position.quantity * position.currentPrice;
                    position.pnlAmount = position.marketValue - position.costValue;
                    position.returnRate = position.costValue > 0 ? (position.pnlAmount / position.costValue) * 100 : 0;
                }
            });

            // 重新渲染当前显示的数据
            if (currentTab === 'stockPool') {
                renderStockPool();
            } else if (currentTab === 'position') {
                renderPositions();
            } else if (currentTab === 'trading' && selectedTradingStock) {
                // 更新选中股票的信息
                const updatedStock = stockPoolData.find(s => s.code === selectedTradingStock.code);
                if (updatedStock) {
                    selectedTradingStock = updatedStock;
                    updateStockInfo();
                    updateDepthInfo();
                }
            }

            updateAccountInfo();
        }

        // 订单类型切换处理
        document.addEventListener('change', function(e) {
            if (e.target.name === 'orderType') {
                const priceField = document.getElementById('priceField');
                if (e.target.value === 'market') {
                    priceField.style.display = 'none';
                } else {
                    priceField.style.display = 'block';
                }
                calculateAmount();
            }
        });

        // 键盘事件处理
        document.addEventListener('keydown', function(e) {
            // ESC键关闭模态框
            if (e.key === 'Escape') {
                hideAddStockModal();
                hideConfirmModal();
            }
        });

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                hideAddStockModal();
                hideConfirmModal();
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeData();
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            setInterval(updateRealtimeData, 3000); // 每3秒更新一次实时数据
        });
    </script>
</body>
</html>
</body>
</html>
