<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票量化分析系统 - 专业版</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome CDN -->
    <link href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- ECharts CDN -->
    <script src="https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js"></script>
    <style>
        :root {
            /* 深色主题变量 */
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border-color: #475569;
            --accent-primary: #3b82f6;
            --accent-secondary: #8b5cf6;

            /* 金融色彩 */
            --price-up: #10b981;
            --price-down: #ef4444;
            --price-flat: #6b7280;
            --volume-color: #f59e0b;

            /* 渐变色 */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* 浅色主题 */
        .light-theme {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-color: #e2e8f0;
        }

        /* 基础样式 */
        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            transition: all 0.3s ease;
        }

        /* 卡片样式 */
        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        /* 渐变背景 */
        .gradient-bg {
            background: var(--gradient-primary);
        }

        .gradient-success {
            background: var(--gradient-success);
        }

        /* 价格颜色 */
        .price-up { color: var(--price-up); }
        .price-down { color: var(--price-down); }
        .price-flat { color: var(--price-flat); }

        /* 数字字体 */
        .mono-font {
            font-family: 'Monaco', 'Consolas', 'SF Mono', monospace;
            font-variant-numeric: tabular-nums;
        }

        /* 导航样式 */
        .nav-item {
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--accent-primary);
            border-radius: 1px;
        }

        /* 按钮样式 */
        .btn-primary {
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        /* 输入框样式 */
        .input-field {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 8px 12px;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 响应式隐藏 */
        .mobile-hidden {
            display: block;
        }

        @media (max-width: 768px) {
            .mobile-hidden {
                display: none;
            }
        }

        /* 图表容器 */
        .chart-container {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 16px;
            height: 400px;
        }

        /* 侧边栏 */
        .sidebar {
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        /* 状态指示器 */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online {
            background: var(--price-up);
            box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
        }

        .status-offline {
            background: var(--price-down);
        }

        /* 数据表格 */
        .data-table {
            background: var(--bg-secondary);
            border-radius: 8px;
            overflow: hidden;
        }

        .data-table th {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            font-weight: 600;
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table tr:hover {
            background: var(--bg-tertiary);
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--bg-tertiary);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- 主容器 -->
    <div class="min-h-screen flex">
        <!-- 侧边栏 -->
        <div class="sidebar w-64 min-h-screen" id="sidebar">
            <!-- Logo区域 -->
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold">量化分析</h1>
                        <p class="text-xs text-gray-400">专业版</p>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="p-4">
                <div class="space-y-2">
                    <button class="nav-item active w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="dashboard">
                        <i class="fas fa-tachometer-alt w-5"></i>
                        <span>仪表板</span>
                    </button>
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="analysis">
                        <i class="fas fa-chart-candlestick w-5"></i>
                        <span>股票分析</span>
                    </button>
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="indicators">
                        <i class="fas fa-calculator w-5"></i>
                        <span>技术指标</span>
                    </button>
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="watchlist">
                        <i class="fas fa-star w-5"></i>
                        <span>自选股</span>
                    </button>
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="data">
                        <i class="fas fa-database w-5"></i>
                        <span>数据管理</span>
                    </button>
                </div>

                <!-- 底部设置 -->
                <div class="mt-8 pt-4 border-t border-gray-700">
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="settings">
                        <i class="fas fa-cog w-5"></i>
                        <span>设置</span>
                    </button>
                    <button class="w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" onclick="toggleTheme()">
                        <i class="fas fa-moon w-5" id="themeIcon"></i>
                        <span>主题切换</span>
                    </button>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部导航栏 -->
            <header class="bg-gray-800 border-b border-gray-700 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="lg:hidden" onclick="toggleSidebar()">
                            <i class="fas fa-bars text-gray-400"></i>
                        </button>
                        <div class="flex items-center space-x-2">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm text-gray-400">实时连接</span>
                        </div>
                    </div>

                    <!-- 搜索框 -->
                    <div class="flex-1 max-w-md mx-8">
                        <div class="relative">
                            <input type="text"
                                   class="input-field w-full pl-10 pr-4"
                                   placeholder="搜索股票代码或名称..."
                                   id="globalSearch">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 右侧工具栏 -->
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-400 mono-font" id="currentTime"></span>
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <span class="text-sm">演示用户</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-6 overflow-auto">
                <!-- 仪表板 -->
                <div id="dashboard" class="tab-content">
                    <!-- 市场概览卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="card p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">上证指数</p>
                                    <p class="text-2xl font-bold mono-font" id="shIndex">3,245.67</p>
                                    <p class="text-sm price-up">+1.23%</p>
                                </div>
                                <div class="w-12 h-12 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-arrow-up text-green-500"></i>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">深证成指</p>
                                    <p class="text-2xl font-bold mono-font" id="szIndex">12,456.89</p>
                                    <p class="text-sm price-down">-0.45%</p>
                                </div>
                                <div class="w-12 h-12 bg-red-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-arrow-down text-red-500"></i>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">创业板指</p>
                                    <p class="text-2xl font-bold mono-font" id="cybIndex">2,789.34</p>
                                    <p class="text-sm price-up">+2.15%</p>
                                </div>
                                <div class="w-12 h-12 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-arrow-up text-green-500"></i>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">活跃股票</p>
                                    <p class="text-2xl font-bold mono-font">1,247</p>
                                    <p class="text-sm text-gray-400">实时监控</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-line text-blue-500"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 热门股票和技术指标概览 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- 热门股票 -->
                        <div class="card p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">热门股票</h3>
                                <button class="text-sm text-blue-400 hover:text-blue-300">查看更多</button>
                            </div>
                            <div class="space-y-3" id="hotStocks">
                                <!-- 热门股票列表将通过JavaScript填充 -->
                            </div>
                        </div>

                        <!-- 技术指标概览 -->
                        <div class="card p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">技术指标概览</h3>
                                <button class="text-sm text-blue-400 hover:text-blue-300">详细分析</button>
                            </div>
                            <div class="space-y-4" id="indicatorOverview">
                                <!-- 指标概览将通过JavaScript填充 -->
                            </div>
                        </div>
                    </div>

                    <!-- 市场趋势图表 -->
                    <div class="card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">市场趋势</h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded">日线</button>
                                <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded">周线</button>
                                <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded">月线</button>
                            </div>
                        </div>
                        <div class="chart-container" id="marketTrendChart"></div>
                    </div>
                </div>

                <!-- 股票分析 -->
                <div id="analysis" class="tab-content hidden">
                    <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
                        <!-- 股票选择面板 -->
                        <div class="xl:col-span-1">
                            <div class="card p-6 mb-6">
                                <h3 class="text-lg font-semibold mb-4">股票选择</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">股票代码</label>
                                        <select class="input-field w-full" id="stockSelector">
                                            <option value="">选择股票</option>
                                            <option value="000001">000001 平安银行</option>
                                            <option value="000002">000002 万科A</option>
                                            <option value="600036">600036 招商银行</option>
                                            <option value="600519">600519 贵州茅台</option>
                                            <option value="000858">000858 五粮液</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">时间周期</label>
                                        <select class="input-field w-full" id="periodSelector">
                                            <option value="D1">日线</option>
                                            <option value="W1">周线</option>
                                            <option value="M1">月线</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">时间范围</label>
                                        <select class="input-field w-full" id="rangeSelector">
                                            <option value="30">最近30天</option>
                                            <option value="60">最近60天</option>
                                            <option value="120">最近120天</option>
                                            <option value="250">最近250天</option>
                                        </select>
                                    </div>

                                    <button class="btn-primary w-full" onclick="loadStockAnalysis()">
                                        <i class="fas fa-chart-line mr-2"></i>
                                        开始分析
                                    </button>
                                </div>
                            </div>

                            <!-- 股票基本信息 -->
                            <div class="card p-6" id="stockInfo" style="display: none;">
                                <h3 class="text-lg font-semibold mb-4">基本信息</h3>
                                <div class="space-y-3" id="stockBasicInfo">
                                    <!-- 股票基本信息将通过JavaScript填充 -->
                                </div>
                            </div>
                        </div>

                        <!-- 主图表区域 -->
                        <div class="xl:col-span-3">
                            <!-- K线图表 -->
                            <div class="card p-6 mb-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-semibold">K线图表</h3>
                                    <div class="flex space-x-2">
                                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded" data-indicator="none">主图</button>
                                        <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded" data-indicator="ma">均线</button>
                                        <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded" data-indicator="bollinger">布林带</button>
                                    </div>
                                </div>
                                <div class="chart-container" id="klineChart" style="height: 500px;"></div>
                            </div>

                            <!-- 技术指标图表 -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div class="card p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-lg font-semibold">MACD指标</h3>
                                        <button class="text-sm text-blue-400 hover:text-blue-300">参数设置</button>
                                    </div>
                                    <div class="chart-container" id="macdChart" style="height: 300px;"></div>
                                </div>

                                <div class="card p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-lg font-semibold">成交量</h3>
                                        <button class="text-sm text-blue-400 hover:text-blue-300">详细分析</button>
                                    </div>
                                    <div class="chart-container" id="volumeChart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 技术指标 -->
                <div id="indicators" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                        <!-- 指标选择 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">指标选择</h3>
                            <div class="space-y-3">
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" checked data-indicator="macd">
                                    <span>MACD</span>
                                </label>
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" data-indicator="kdj">
                                    <span>KDJ</span>
                                </label>
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" data-indicator="rsi">
                                    <span>RSI</span>
                                </label>
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" data-indicator="bollinger">
                                    <span>Bollinger Bands</span>
                                </label>
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" data-indicator="arbr">
                                    <span>ARBR</span>
                                </label>
                            </div>
                        </div>

                        <!-- 参数设置 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">参数设置</h3>
                            <div class="space-y-4" id="indicatorParams">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">MACD参数</label>
                                    <div class="grid grid-cols-3 gap-2">
                                        <input type="number" class="input-field text-sm" value="12" placeholder="快线">
                                        <input type="number" class="input-field text-sm" value="26" placeholder="慢线">
                                        <input type="number" class="input-field text-sm" value="9" placeholder="信号线">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 信号提示 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">交易信号</h3>
                            <div class="space-y-3" id="tradingSignals">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span class="text-sm">MACD金叉</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                    <span class="text-sm">RSI超买</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <span class="text-sm">KDJ死叉</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 指标图表网格 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">MACD指标</h3>
                            <div class="chart-container" id="indicatorMacdChart"></div>
                        </div>

                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">KDJ指标</h3>
                            <div class="chart-container" id="indicatorKdjChart"></div>
                        </div>

                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">RSI指标</h3>
                            <div class="chart-container" id="indicatorRsiChart"></div>
                        </div>

                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">Bollinger Bands</h3>
                            <div class="chart-container" id="indicatorBollingerChart"></div>
                        </div>
                    </div>
                </div>

                <!-- 自选股 -->
                <div id="watchlist" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 自选股列表 -->
                        <div class="lg:col-span-2">
                            <div class="card p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-semibold">我的自选股</h3>
                                    <button class="btn-primary" onclick="showAddWatchlistModal()">
                                        <i class="fas fa-plus mr-2"></i>添加股票
                                    </button>
                                </div>

                                <div class="data-table">
                                    <table class="w-full">
                                        <thead>
                                            <tr>
                                                <th>代码</th>
                                                <th>名称</th>
                                                <th>现价</th>
                                                <th>涨跌幅</th>
                                                <th>成交量</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="watchlistTable">
                                            <!-- 自选股数据将通过JavaScript填充 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 快速分析 -->
                        <div class="space-y-6">
                            <div class="card p-6">
                                <h3 class="text-lg font-semibold mb-4">快速分析</h3>
                                <div class="space-y-3" id="quickAnalysis">
                                    <div class="text-sm">
                                        <span class="text-gray-400">强势股票:</span>
                                        <span class="text-green-400">3只</span>
                                    </div>
                                    <div class="text-sm">
                                        <span class="text-gray-400">弱势股票:</span>
                                        <span class="text-red-400">2只</span>
                                    </div>
                                    <div class="text-sm">
                                        <span class="text-gray-400">震荡股票:</span>
                                        <span class="text-yellow-400">1只</span>
                                    </div>
                                </div>
                            </div>

                            <div class="card p-6">
                                <h3 class="text-lg font-semibold mb-4">预警提醒</h3>
                                <div class="space-y-3" id="alertList">
                                    <div class="flex items-center space-x-3 p-3 bg-red-500 bg-opacity-20 rounded-lg">
                                        <i class="fas fa-exclamation-triangle text-red-400"></i>
                                        <div class="text-sm">
                                            <div class="font-medium">000001 平安银行</div>
                                            <div class="text-gray-400">跌破支撑位</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据管理 -->
                <div id="data" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- 数据源状态 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">数据源状态</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-green-500 bg-opacity-20 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="status-indicator status-online"></div>
                                        <span>Tushare API</span>
                                    </div>
                                    <span class="text-sm text-green-400">正常</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-green-500 bg-opacity-20 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="status-indicator status-online"></div>
                                        <span>AkShare API</span>
                                    </div>
                                    <span class="text-sm text-green-400">正常</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-yellow-500 bg-opacity-20 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="status-indicator" style="background: #f59e0b;"></div>
                                        <span>Yahoo Finance</span>
                                    </div>
                                    <span class="text-sm text-yellow-400">延迟</span>
                                </div>
                            </div>
                        </div>

                        <!-- 数据统计 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">数据统计</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">股票总数</span>
                                    <span class="mono-font">4,856</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">今日更新</span>
                                    <span class="mono-font">4,823</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">历史数据</span>
                                    <span class="mono-font">2.3TB</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">最后更新</span>
                                    <span class="mono-font" id="lastUpdate">15:30:00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 股票列表管理 -->
                    <div class="card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">股票列表管理</h3>
                            <div class="flex space-x-2">
                                <button class="btn-primary">
                                    <i class="fas fa-sync mr-2"></i>刷新数据
                                </button>
                                <button class="btn-primary">
                                    <i class="fas fa-download mr-2"></i>导出数据
                                </button>
                            </div>
                        </div>

                        <!-- 搜索和筛选 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                            <input type="text" class="input-field" placeholder="搜索股票代码或名称" id="stockSearchInput">
                            <select class="input-field" id="industryFilter">
                                <option value="">所有行业</option>
                                <option value="银行">银行</option>
                                <option value="房地产">房地产</option>
                                <option value="白酒">白酒</option>
                                <option value="科技">科技</option>
                            </select>
                            <select class="input-field" id="marketFilter">
                                <option value="">所有市场</option>
                                <option value="SH">上海</option>
                                <option value="SZ">深圳</option>
                                <option value="CYB">创业板</option>
                            </select>
                            <button class="btn-primary" onclick="filterStocks()">
                                <i class="fas fa-filter mr-2"></i>筛选
                            </button>
                        </div>

                        <!-- 股票数据表格 -->
                        <div class="data-table">
                            <table class="w-full">
                                <thead>
                                    <tr>
                                        <th>代码</th>
                                        <th>名称</th>
                                        <th>行业</th>
                                        <th>市场</th>
                                        <th>现价</th>
                                        <th>涨跌幅</th>
                                        <th>成交量</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="stockDataTable">
                                    <!-- 股票数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="flex items-center justify-between mt-4">
                            <span class="text-sm text-gray-400">显示 1-20 条，共 4,856 条记录</span>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700">上一页</button>
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded">1</button>
                                <button class="px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700">2</button>
                                <button class="px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700">3</button>
                                <button class="px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设置 -->
                <div id="settings" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 界面设置 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">界面设置</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">主题模式</label>
                                    <select class="input-field w-full" id="themeSelect">
                                        <option value="dark">深色主题</option>
                                        <option value="light">浅色主题</option>
                                        <option value="auto">跟随系统</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">语言设置</label>
                                    <select class="input-field w-full">
                                        <option value="zh-CN">简体中文</option>
                                        <option value="en-US">English</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" class="form-checkbox" checked>
                                        <span>启用实时数据更新</span>
                                    </label>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" class="form-checkbox">
                                        <span>启用声音提醒</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 数据设置 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">数据设置</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">数据更新频率</label>
                                    <select class="input-field w-full">
                                        <option value="1">1秒</option>
                                        <option value="3" selected>3秒</option>
                                        <option value="5">5秒</option>
                                        <option value="10">10秒</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">默认K线周期</label>
                                    <select class="input-field w-full">
                                        <option value="D1" selected>日线</option>
                                        <option value="W1">周线</option>
                                        <option value="M1">月线</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">历史数据范围</label>
                                    <select class="input-field w-full">
                                        <option value="30">30天</option>
                                        <option value="60">60天</option>
                                        <option value="120" selected>120天</option>
                                        <option value="250">250天</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 技术指标设置 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">技术指标设置</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">MACD参数</label>
                                    <div class="grid grid-cols-3 gap-2">
                                        <input type="number" class="input-field text-sm" value="12" placeholder="快线">
                                        <input type="number" class="input-field text-sm" value="26" placeholder="慢线">
                                        <input type="number" class="input-field text-sm" value="9" placeholder="信号线">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">KDJ参数</label>
                                    <div class="grid grid-cols-3 gap-2">
                                        <input type="number" class="input-field text-sm" value="9" placeholder="K值">
                                        <input type="number" class="input-field text-sm" value="3" placeholder="D值">
                                        <input type="number" class="input-field text-sm" value="3" placeholder="J值">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">RSI参数</label>
                                    <input type="number" class="input-field w-full" value="14" placeholder="周期">
                                </div>
                            </div>
                        </div>

                        <!-- 系统信息 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">系统信息</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">版本号</span>
                                    <span class="mono-font">v2.1.0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">构建时间</span>
                                    <span class="mono-font">2024-06-14</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">API版本</span>
                                    <span class="mono-font">v1.0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">数据库</span>
                                    <span class="mono-font">MySQL 8.0</span>
                                </div>
                            </div>

                            <div class="mt-6 pt-4 border-t border-gray-700">
                                <button class="btn-primary w-full">
                                    <i class="fas fa-save mr-2"></i>保存设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="addWatchlistModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="card p-6 w-full max-w-md">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">添加自选股</h3>
                    <button onclick="hideAddWatchlistModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm text-gray-400 mb-2">股票代码</label>
                        <input type="text" class="input-field w-full" placeholder="例如: 000001" id="newWatchlistCode">
                    </div>
                    <div>
                        <label class="block text-sm text-gray-400 mb-2">股票名称</label>
                        <input type="text" class="input-field w-full" placeholder="例如: 平安银行" id="newWatchlistName">
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="hideAddWatchlistModal()" class="flex-1 px-4 py-2 border border-gray-600 rounded hover:bg-gray-700">
                            取消
                        </button>
                        <button onclick="addToWatchlist()" class="flex-1 btn-primary">
                            添加
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量和状态管理
        class StockAnalysisApp {
            constructor() {
                this.currentTab = 'dashboard';
                this.currentTheme = 'dark';
                this.selectedStock = null;
                this.charts = {};
                this.apiBaseUrl = 'http://localhost:8000/api/v1'; // 后端API地址
                this.isRealTimeEnabled = true;
                this.updateInterval = 3000; // 3秒更新一次

                // 数据存储
                this.stockData = [];
                this.watchlistData = [];
                this.marketData = {
                    shIndex: { value: 3245.67, change: 1.23 },
                    szIndex: { value: 12456.89, change: -0.45 },
                    cybIndex: { value: 2789.34, change: 2.15 }
                };

                this.init();
            }

            // 初始化应用
            init() {
                this.setupEventListeners();
                this.loadInitialData();
                this.startRealTimeUpdates();
                this.updateCurrentTime();
                setInterval(() => this.updateCurrentTime(), 1000);
            }

            // 设置事件监听器
            setupEventListeners() {
                // 导航切换
                document.querySelectorAll('.nav-item').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const tab = e.currentTarget.dataset.tab;
                        if (tab) this.switchTab(tab);
                    });
                });

                // 股票选择器
                const stockSelector = document.getElementById('stockSelector');
                if (stockSelector) {
                    stockSelector.addEventListener('change', (e) => {
                        this.selectStock(e.target.value);
                    });
                }

                // 全局搜索
                const globalSearch = document.getElementById('globalSearch');
                if (globalSearch) {
                    globalSearch.addEventListener('input', (e) => {
                        this.performGlobalSearch(e.target.value);
                    });
                }

                // 主图指标切换
                document.querySelectorAll('[data-indicator]').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.toggleIndicator(e.target.dataset.indicator);
                    });
                });
            }

            // 加载初始数据
            async loadInitialData() {
                try {
                    // 模拟API调用，实际应用中替换为真实API
                    this.stockData = await this.mockApiCall('/stocks/list', [
                        {
                            code: '000001',
                            name: '平安银行',
                            industry: '银行',
                            market: 'SZ',
                            currentPrice: 12.85,
                            openPrice: 12.90,
                            highPrice: 13.10,
                            lowPrice: 12.80,
                            prevClose: 12.88,
                            changeAmount: -0.03,
                            changePercent: -0.23,
                            volume: 15420000,
                            turnover: 198450000
                        },
                        {
                            code: '000002',
                            name: '万科A',
                            industry: '房地产',
                            market: 'SZ',
                            currentPrice: 18.45,
                            openPrice: 18.20,
                            highPrice: 18.68,
                            lowPrice: 18.15,
                            prevClose: 18.30,
                            changeAmount: 0.15,
                            changePercent: 0.82,
                            volume: 8950000,
                            turnover: 164820000
                        },
                        {
                            code: '600036',
                            name: '招商银行',
                            industry: '银行',
                            market: 'SH',
                            currentPrice: 35.68,
                            openPrice: 35.20,
                            highPrice: 36.15,
                            lowPrice: 35.10,
                            prevClose: 35.45,
                            changeAmount: 0.23,
                            changePercent: 0.65,
                            volume: 12350000,
                            turnover: 441230000
                        },
                        {
                            code: '600519',
                            name: '贵州茅台',
                            industry: '白酒',
                            market: 'SH',
                            currentPrice: 1685.50,
                            openPrice: 1680.00,
                            highPrice: 1695.80,
                            lowPrice: 1675.20,
                            prevClose: 1678.90,
                            changeAmount: 6.60,
                            changePercent: 0.39,
                            volume: 1250000,
                            turnover: 2108750000
                        },
                        {
                            code: '000858',
                            name: '五粮液',
                            industry: '白酒',
                            market: 'SZ',
                            currentPrice: 128.45,
                            openPrice: 129.80,
                            highPrice: 130.20,
                            lowPrice: 127.90,
                            prevClose: 129.60,
                            changeAmount: -1.15,
                            changePercent: -0.89,
                            volume: 3680000,
                            turnover: 473240000
                        }
                    ]);

                    // 初始化自选股
                    this.watchlistData = this.stockData.slice(0, 3);

                    // 渲染初始界面
                    this.renderDashboard();
                    this.renderWatchlist();
                    this.renderStockDataTable();
                    this.updateStockSelector();

                } catch (error) {
                    console.error('加载初始数据失败:', error);
                    this.showNotification('数据加载失败，请检查网络连接', 'error');
                }
            }

            // 模拟API调用
            async mockApiCall(endpoint, mockData) {
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 100));
                return mockData;
            }

            // 真实API调用（当后端可用时使用）
            async apiCall(endpoint, options = {}) {
                try {
                    const response = await fetch(`${this.apiBaseUrl}${endpoint}`, {
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers
                        },
                        ...options
                    });

                    if (!response.ok) {
                        throw new Error(`API调用失败: ${response.status}`);
                    }

                    return await response.json();
                } catch (error) {
                    console.error('API调用错误:', error);
                    throw error;
                }
            }

            // 切换标签页
            switchTab(tabName) {
                // 更新导航状态
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

                // 显示对应内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(tabName).classList.remove('hidden');

                this.currentTab = tabName;

                // 根据标签页加载相应数据
                switch(tabName) {
                    case 'analysis':
                        this.initializeCharts();
                        break;
                    case 'indicators':
                        this.loadIndicatorCharts();
                        break;
                    case 'data':
                        this.renderStockDataTable();
                        break;
                }
            }

            // 渲染仪表板
            renderDashboard() {
                this.renderHotStocks();
                this.renderIndicatorOverview();
                this.renderMarketTrendChart();
                this.updateMarketIndices();
            }

            // 渲染热门股票
            renderHotStocks() {
                const container = document.getElementById('hotStocks');
                if (!container) return;

                const hotStocks = this.stockData
                    .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent))
                    .slice(0, 5);

                container.innerHTML = hotStocks.map(stock => `
                    <div class="flex items-center justify-between p-3 hover:bg-gray-700 rounded-lg cursor-pointer"
                         onclick="app.selectStockFromDashboard('${stock.code}')">
                        <div>
                            <div class="font-medium">${stock.code} ${stock.name}</div>
                            <div class="text-sm text-gray-400">${stock.industry}</div>
                        </div>
                        <div class="text-right">
                            <div class="mono-font">${this.formatNumber(stock.currentPrice)}</div>
                            <div class="text-sm ${this.getPriceClass(stock.changePercent)}">
                                ${stock.changePercent >= 0 ? '+' : ''}${this.formatNumber(stock.changePercent)}%
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            // 渲染技术指标概览
            renderIndicatorOverview() {
                const container = document.getElementById('indicatorOverview');
                if (!container) return;

                const indicators = [
                    { name: 'MACD', status: 'bullish', signal: '金叉' },
                    { name: 'KDJ', status: 'bearish', signal: '死叉' },
                    { name: 'RSI', status: 'neutral', signal: '中性' },
                    { name: 'BOLL', status: 'bullish', signal: '突破上轨' }
                ];

                container.innerHTML = indicators.map(indicator => `
                    <div class="flex items-center justify-between">
                        <span class="text-sm">${indicator.name}</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs ${
                                indicator.status === 'bullish' ? 'text-green-400' :
                                indicator.status === 'bearish' ? 'text-red-400' : 'text-yellow-400'
                            }">${indicator.signal}</span>
                            <div class="w-2 h-2 rounded-full ${
                                indicator.status === 'bullish' ? 'bg-green-400' :
                                indicator.status === 'bearish' ? 'bg-red-400' : 'bg-yellow-400'
                            }"></div>
                        </div>
                    </div>
                `).join('');
            }

            // 渲染市场趋势图表
            renderMarketTrendChart() {
                const container = document.getElementById('marketTrendChart');
                if (!container) return;

                // 生成模拟的市场趋势数据
                const dates = [];
                const values = [];
                const baseValue = 3200;

                for (let i = 30; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    dates.push(date.toISOString().split('T')[0]);

                    const randomChange = (Math.random() - 0.5) * 100;
                    const value = baseValue + randomChange + (Math.random() * 200 - 100);
                    values.push(value.toFixed(2));
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: dates,
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8' }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8' },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [{
                        data: values,
                        type: 'line',
                        smooth: true,
                        lineStyle: { color: '#3b82f6', width: 2 },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                                    { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
                                ]
                            }
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    }
                };

                chart.setOption(option);
                this.charts.marketTrend = chart;
            }

            // 更新市场指数
            updateMarketIndices() {
                const indices = ['shIndex', 'szIndex', 'cybIndex'];
                indices.forEach(index => {
                    const element = document.getElementById(index);
                    if (element) {
                        // 模拟价格波动
                        const currentValue = parseFloat(element.textContent.replace(',', ''));
                        const change = (Math.random() - 0.5) * 20;
                        const newValue = currentValue + change;
                        element.textContent = this.formatNumber(newValue);
                    }
                });
            }

            // 选择股票（从仪表板）
            selectStockFromDashboard(stockCode) {
                this.switchTab('analysis');
                const stockSelector = document.getElementById('stockSelector');
                if (stockSelector) {
                    stockSelector.value = stockCode;
                    this.selectStock(stockCode);
                }
            }

            // 选择股票进行分析
            async selectStock(stockCode) {
                if (!stockCode) {
                    this.selectedStock = null;
                    document.getElementById('stockInfo').style.display = 'none';
                    return;
                }

                this.selectedStock = this.stockData.find(stock => stock.code === stockCode);
                if (this.selectedStock) {
                    this.renderStockInfo();
                    await this.loadStockAnalysis();
                }
            }

            // 渲染股票基本信息
            renderStockInfo() {
                const container = document.getElementById('stockBasicInfo');
                const infoPanel = document.getElementById('stockInfo');

                if (!container || !this.selectedStock) return;

                const stock = this.selectedStock;
                const changeClass = this.getPriceClass(stock.changePercent);

                container.innerHTML = `
                    <div class="text-lg font-semibold mb-2">${stock.name} (${stock.code})</div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-400">现价</span>
                            <span class="mono-font ${changeClass}">${this.formatNumber(stock.currentPrice)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">涨跌幅</span>
                            <span class="mono-font ${changeClass}">
                                ${stock.changePercent >= 0 ? '+' : ''}${this.formatNumber(stock.changePercent)}%
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">成交量</span>
                            <span class="mono-font">${this.formatVolume(stock.volume)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">行业</span>
                            <span>${stock.industry}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">市场</span>
                            <span>${stock.market}</span>
                        </div>
                    </div>
                `;

                infoPanel.style.display = 'block';
            }

            // 加载股票分析数据
            async loadStockAnalysis() {
                if (!this.selectedStock) return;

                try {
                    // 模拟加载K线数据
                    const klineData = await this.generateMockKlineData(this.selectedStock.code);
                    this.renderKlineChart(klineData);

                    // 加载技术指标
                    const macdData = await this.generateMockMacdData();
                    this.renderMacdChart(macdData);

                    const volumeData = await this.generateMockVolumeData();
                    this.renderVolumeChart(volumeData);

                } catch (error) {
                    console.error('加载股票分析数据失败:', error);
                    this.showNotification('分析数据加载失败', 'error');
                }
            }

            // 生成模拟K线数据
            async generateMockKlineData(stockCode) {
                const data = [];
                const basePrice = this.selectedStock.currentPrice;
                let currentPrice = basePrice;

                for (let i = 120; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);

                    const open = currentPrice;
                    const change = (Math.random() - 0.5) * basePrice * 0.05;
                    const close = Math.max(0.01, open + change);
                    const high = Math.max(open, close) * (1 + Math.random() * 0.03);
                    const low = Math.min(open, close) * (1 - Math.random() * 0.03);
                    const volume = Math.floor(Math.random() * 10000000) + 1000000;

                    data.push([
                        date.toISOString().split('T')[0],
                        open.toFixed(2),
                        close.toFixed(2),
                        low.toFixed(2),
                        high.toFixed(2),
                        volume
                    ]);

                    currentPrice = close;
                }

                return data;
            }

            // 渲染K线图表
            renderKlineChart(data) {
                const container = document.getElementById('klineChart');
                if (!container) return;

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item[0]),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8' }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true,
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8' },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [{
                        type: 'candlestick',
                        data: data.map(item => [item[1], item[2], item[3], item[4]]),
                        itemStyle: {
                            color: '#10b981',
                            color0: '#ef4444',
                            borderColor: '#10b981',
                            borderColor0: '#ef4444'
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' },
                        formatter: function(params) {
                            const data = params[0].data;
                            return `
                                日期: ${params[0].axisValue}<br/>
                                开盘: ${data[0]}<br/>
                                收盘: ${data[1]}<br/>
                                最低: ${data[2]}<br/>
                                最高: ${data[3]}
                            `;
                        }
                    },
                    dataZoom: [{
                        type: 'inside',
                        start: 70,
                        end: 100
                    }]
                };

                chart.setOption(option);
                this.charts.kline = chart;
            }

            // 生成模拟MACD数据
            async generateMockMacdData() {
                const data = [];
                for (let i = 0; i < 120; i++) {
                    const date = new Date();
                    date.setDate(date.getDate() - (120 - i));

                    data.push({
                        date: date.toISOString().split('T')[0],
                        dif: (Math.random() - 0.5) * 2,
                        dea: (Math.random() - 0.5) * 1.5,
                        macd: (Math.random() - 0.5) * 0.5
                    });
                }
                return data;
            }

            // 渲染MACD图表
            renderMacdChart(data) {
                const container = document.getElementById('macdChart');
                if (!container) return;

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [
                        {
                            name: 'DIF',
                            type: 'line',
                            data: data.map(item => item.dif),
                            lineStyle: { color: '#3b82f6', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'DEA',
                            type: 'line',
                            data: data.map(item => item.dea),
                            lineStyle: { color: '#f59e0b', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'MACD',
                            type: 'bar',
                            data: data.map(item => item.macd),
                            itemStyle: {
                                color: function(params) {
                                    return params.value >= 0 ? '#10b981' : '#ef4444';
                                }
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    },
                    legend: {
                        textStyle: { color: '#94a3b8' },
                        top: 10
                    }
                };

                chart.setOption(option);
                this.charts.macd = chart;
            }

            // 生成模拟成交量数据
            async generateMockVolumeData() {
                const data = [];
                for (let i = 0; i < 120; i++) {
                    const date = new Date();
                    date.setDate(date.getDate() - (120 - i));

                    data.push({
                        date: date.toISOString().split('T')[0],
                        volume: Math.floor(Math.random() * 10000000) + 1000000,
                        price: this.selectedStock.currentPrice + (Math.random() - 0.5) * 5
                    });
                }
                return data;
            }

            // 渲染成交量图表
            renderVolumeChart(data) {
                const container = document.getElementById('volumeChart');
                if (!container) return;

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [{
                        type: 'bar',
                        data: data.map(item => item.volume),
                        itemStyle: {
                            color: function(params) {
                                return params.dataIndex % 2 === 0 ? '#10b981' : '#ef4444';
                            }
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' },
                        formatter: function(params) {
                            const data = params[0];
                            return `
                                日期: ${data.axisValue}<br/>
                                成交量: ${(data.value / 10000).toFixed(0)}万
                            `;
                        }
                    }
                };

                chart.setOption(option);
                this.charts.volume = chart;
            }

            // 渲染自选股列表
            renderWatchlist() {
                const tbody = document.getElementById('watchlistTable');
                if (!tbody) return;

                tbody.innerHTML = this.watchlistData.map(stock => {
                    const changeClass = this.getPriceClass(stock.changePercent);
                    const changePrefix = stock.changePercent >= 0 ? '+' : '';

                    return `
                        <tr class="hover:bg-gray-700">
                            <td class="mono-font">${stock.code}</td>
                            <td>${stock.name}</td>
                            <td class="mono-font ${changeClass}">${this.formatNumber(stock.currentPrice)}</td>
                            <td class="mono-font ${changeClass}">${changePrefix}${this.formatNumber(stock.changePercent)}%</td>
                            <td class="mono-font">${this.formatVolume(stock.volume)}</td>
                            <td>
                                <button class="text-blue-400 hover:text-blue-300 mr-2" onclick="app.analyzeStock('${stock.code}')">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                                <button class="text-red-400 hover:text-red-300" onclick="app.removeFromWatchlist('${stock.code}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            // 渲染股票数据表格
            renderStockDataTable() {
                const tbody = document.getElementById('stockDataTable');
                if (!tbody) return;

                tbody.innerHTML = this.stockData.slice(0, 20).map(stock => {
                    const changeClass = this.getPriceClass(stock.changePercent);
                    const changePrefix = stock.changePercent >= 0 ? '+' : '';

                    return `
                        <tr class="hover:bg-gray-700">
                            <td class="mono-font">${stock.code}</td>
                            <td>${stock.name}</td>
                            <td>${stock.industry}</td>
                            <td>${stock.market}</td>
                            <td class="mono-font ${changeClass}">${this.formatNumber(stock.currentPrice)}</td>
                            <td class="mono-font ${changeClass}">${changePrefix}${this.formatNumber(stock.changePercent)}%</td>
                            <td class="mono-font">${this.formatVolume(stock.volume)}</td>
                            <td>
                                <button class="text-blue-400 hover:text-blue-300 mr-2" onclick="app.addToWatchlist('${stock.code}')">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300" onclick="app.analyzeStock('${stock.code}')">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            // 更新股票选择器
            updateStockSelector() {
                const selector = document.getElementById('stockSelector');
                if (!selector) return;

                selector.innerHTML = '<option value="">选择股票</option>' +
                    this.stockData.map(stock =>
                        `<option value="${stock.code}">${stock.code} ${stock.name}</option>`
                    ).join('');
            }

            // 分析股票
            analyzeStock(stockCode) {
                this.switchTab('analysis');
                const stockSelector = document.getElementById('stockSelector');
                if (stockSelector) {
                    stockSelector.value = stockCode;
                    this.selectStock(stockCode);
                }
            }

            // 添加到自选股
            addToWatchlist(stockCode) {
                const stock = this.stockData.find(s => s.code === stockCode);
                if (stock && !this.watchlistData.find(w => w.code === stockCode)) {
                    this.watchlistData.push(stock);
                    this.renderWatchlist();
                    this.showNotification('已添加到自选股', 'success');
                } else {
                    this.showNotification('股票已在自选股中', 'warning');
                }
            }

            // 从自选股移除
            removeFromWatchlist(stockCode) {
                this.watchlistData = this.watchlistData.filter(stock => stock.code !== stockCode);
                this.renderWatchlist();
                this.showNotification('已从自选股移除', 'success');
            }

            // 初始化图表
            initializeCharts() {
                // 确保图表容器存在后再初始化
                setTimeout(() => {
                    if (this.selectedStock) {
                        this.loadStockAnalysis();
                    }
                }, 100);
            }

            // 加载指标图表
            loadIndicatorCharts() {
                // 加载各种技术指标图表
                this.renderIndicatorMacd();
                this.renderIndicatorKdj();
                this.renderIndicatorRsi();
                this.renderIndicatorBollinger();
            }

            // 渲染MACD指标图表
            renderIndicatorMacd() {
                const container = document.getElementById('indicatorMacdChart');
                if (!container) return;

                // 生成模拟MACD数据
                const data = [];
                for (let i = 0; i < 60; i++) {
                    data.push({
                        date: new Date(Date.now() - (60 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        dif: (Math.random() - 0.5) * 2,
                        dea: (Math.random() - 0.5) * 1.5,
                        macd: (Math.random() - 0.5) * 0.5
                    });
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [
                        {
                            name: 'DIF',
                            type: 'line',
                            data: data.map(item => item.dif),
                            lineStyle: { color: '#3b82f6', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'DEA',
                            type: 'line',
                            data: data.map(item => item.dea),
                            lineStyle: { color: '#f59e0b', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'MACD',
                            type: 'bar',
                            data: data.map(item => item.macd),
                            itemStyle: {
                                color: function(params) {
                                    return params.value >= 0 ? '#10b981' : '#ef4444';
                                }
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    },
                    legend: {
                        textStyle: { color: '#94a3b8' },
                        top: 10
                    }
                };

                chart.setOption(option);
                this.charts.indicatorMacd = chart;
            }

            // 渲染KDJ指标图表
            renderIndicatorKdj() {
                const container = document.getElementById('indicatorKdjChart');
                if (!container) return;

                const data = [];
                for (let i = 0; i < 60; i++) {
                    data.push({
                        date: new Date(Date.now() - (60 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        k: Math.random() * 100,
                        d: Math.random() * 100,
                        j: Math.random() * 100
                    });
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        min: 0,
                        max: 100,
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [
                        {
                            name: 'K',
                            type: 'line',
                            data: data.map(item => item.k),
                            lineStyle: { color: '#3b82f6', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'D',
                            type: 'line',
                            data: data.map(item => item.d),
                            lineStyle: { color: '#f59e0b', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'J',
                            type: 'line',
                            data: data.map(item => item.j),
                            lineStyle: { color: '#8b5cf6', width: 1 },
                            symbol: 'none'
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    },
                    legend: {
                        textStyle: { color: '#94a3b8' },
                        top: 10
                    }
                };

                chart.setOption(option);
                this.charts.indicatorKdj = chart;
            }

            // 渲染RSI指标图表
            renderIndicatorRsi() {
                const container = document.getElementById('indicatorRsiChart');
                if (!container) return;

                const data = [];
                for (let i = 0; i < 60; i++) {
                    data.push({
                        date: new Date(Date.now() - (60 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        rsi: Math.random() * 100
                    });
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        min: 0,
                        max: 100,
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [{
                        name: 'RSI',
                        type: 'line',
                        data: data.map(item => item.rsi),
                        lineStyle: { color: '#8b5cf6', width: 2 },
                        symbol: 'none',
                        markLine: {
                            data: [
                                { yAxis: 70, lineStyle: { color: '#ef4444', type: 'dashed' } },
                                { yAxis: 30, lineStyle: { color: '#10b981', type: 'dashed' } }
                            ]
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    }
                };

                chart.setOption(option);
                this.charts.indicatorRsi = chart;
            }

            // 渲染Bollinger Bands指标图表
            renderIndicatorBollinger() {
                const container = document.getElementById('indicatorBollingerChart');
                if (!container) return;

                const data = [];
                const basePrice = 100;
                for (let i = 0; i < 60; i++) {
                    const price = basePrice + (Math.random() - 0.5) * 20;
                    data.push({
                        date: new Date(Date.now() - (60 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        price: price,
                        upper: price + Math.random() * 5 + 2,
                        lower: price - Math.random() * 5 - 2,
                        middle: price + (Math.random() - 0.5) * 2
                    });
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [
                        {
                            name: '上轨',
                            type: 'line',
                            data: data.map(item => item.upper),
                            lineStyle: { color: '#ef4444', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: '中轨',
                            type: 'line',
                            data: data.map(item => item.middle),
                            lineStyle: { color: '#3b82f6', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: '下轨',
                            type: 'line',
                            data: data.map(item => item.lower),
                            lineStyle: { color: '#10b981', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: '价格',
                            type: 'line',
                            data: data.map(item => item.price),
                            lineStyle: { color: '#f59e0b', width: 2 },
                            symbol: 'none'
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    },
                    legend: {
                        textStyle: { color: '#94a3b8' },
                        top: 10
                    }
                };

                chart.setOption(option);
                this.charts.indicatorBollinger = chart;
            }

            // 全局搜索功能
            performGlobalSearch(query) {
                if (!query.trim()) return;

                const results = this.stockData.filter(stock =>
                    stock.code.toLowerCase().includes(query.toLowerCase()) ||
                    stock.name.toLowerCase().includes(query.toLowerCase())
                );

                if (results.length > 0) {
                    // 显示搜索结果（这里可以实现一个下拉搜索结果）
                    console.log('搜索结果:', results);
                }
            }

            // 切换指标显示
            toggleIndicator(indicator) {
                // 更新按钮状态
                document.querySelectorAll('[data-indicator]').forEach(btn => {
                    btn.classList.remove('bg-blue-600', 'text-white');
                    btn.classList.add('text-gray-400');
                });

                const activeBtn = document.querySelector(`[data-indicator="${indicator}"]`);
                if (activeBtn) {
                    activeBtn.classList.add('bg-blue-600', 'text-white');
                    activeBtn.classList.remove('text-gray-400');
                }

                // 重新渲染K线图（这里可以添加不同的指标叠加）
                if (this.selectedStock) {
                    this.loadStockAnalysis();
                }
            }
            const select = document.getElementById('tradingStockSelect');
            select.innerHTML = '<option value="">请选择股票</option>';

            stockPoolData.forEach(stock => {
                const option = document.createElement('option');
                option.value = stock.code;
                option.textContent = `${stock.code} ${stock.name}`;
                select.appendChild(option);
            });
        }

        function selectTradingStock() {
            const stockCode = document.getElementById('tradingStockSelect').value;
            if (!stockCode) {
                document.getElementById('stockInfo').classList.add('hidden');
                document.getElementById('depthInfo').classList.add('hidden');
                selectedTradingStock = null;
                return;
            }

            selectedTradingStock = stockPoolData.find(stock => stock.code === stockCode);
            if (selectedTradingStock) {
                updateStockInfo();
                updateDepthInfo();
                document.getElementById('stockInfo').classList.remove('hidden');
                document.getElementById('depthInfo').classList.remove('hidden');
            }
        }

        function updateStockInfo() {
            if (!selectedTradingStock) return;

            const stock = selectedTradingStock;
            const changeClass = getPriceClass(stock.changePercent);
            const changePrefix = stock.changeAmount >= 0 ? '+' : '';

            document.getElementById('stockName').textContent = `${stock.name} (${stock.code})`;
            document.getElementById('stockPrice').textContent = formatNumber(stock.currentPrice);
            document.getElementById('stockPrice').className = `text-2xl font-bold ${changeClass}`;
            document.getElementById('stockChange').textContent = `${changePrefix}${formatNumber(stock.changeAmount)} (${changePrefix}${formatNumber(stock.changePercent)}%)`;
            document.getElementById('stockChange').className = `text-sm ${changeClass}`;

            document.getElementById('openPrice').textContent = formatNumber(stock.openPrice);
            document.getElementById('prevClose').textContent = formatNumber(stock.prevClose);
            document.getElementById('highPrice').textContent = formatNumber(stock.highPrice);
            document.getElementById('lowPrice').textContent = formatNumber(stock.lowPrice);
        }

        function updateDepthInfo() {
            if (!selectedTradingStock) return;

            // 模拟五档行情数据
            const currentPrice = selectedTradingStock.currentPrice;
            const sellDepth = [];
            const buyDepth = [];

            // 生成卖档数据
            for (let i = 5; i >= 1; i--) {
                sellDepth.push({
                    level: i,
                    price: currentPrice + (i * 0.01),
                    volume: Math.floor(Math.random() * 5000) + 1000
                });
            }

            // 生成买档数据
            for (let i = 1; i <= 5; i++) {
                buyDepth.push({
                    level: i,
                    price: currentPrice - (i * 0.01),
                    volume: Math.floor(Math.random() * 5000) + 1000
                });
            }

            // 渲染卖档
            const sellDepthDiv = document.getElementById('sellDepth');
            sellDepthDiv.innerHTML = '';
            sellDepth.forEach(item => {
                const div = document.createElement('div');
                div.className = 'depth-row sell-row';
                div.innerHTML = `
                    <span>卖${item.level}</span>
                    <span class="price-down">${formatNumber(item.price)}</span>
                    <span>${item.volume}</span>
                `;
                sellDepthDiv.appendChild(div);
            });

            // 渲染买档
            const buyDepthDiv = document.getElementById('buyDepth');
            buyDepthDiv.innerHTML = '';
            buyDepth.forEach(item => {
                const div = document.createElement('div');
                div.className = 'depth-row buy-row';
                div.innerHTML = `
                    <span>买${item.level}</span>
                    <span class="price-up">${formatNumber(item.price)}</span>
                    <span>${item.volume}</span>
                `;
                buyDepthDiv.appendChild(div);
            });
        }

        function setTradeDirection(direction) {
            tradeDirection = direction;

            const buyTab = document.getElementById('buyTab');
            const sellTab = document.getElementById('sellTab');
            const submitBtn = document.getElementById('submitOrderBtn');
            const availableLabel = document.getElementById('availableLabel');
            const availableAmount = document.getElementById('availableAmount');

            if (direction === 'buy') {
                buyTab.className = 'flex-1 py-2 px-4 bg-red-500 text-white rounded-l buy-btn';
                sellTab.className = 'flex-1 py-2 px-4 bg-gray-300 text-gray-700 rounded-r';
                submitBtn.textContent = '买入';
                submitBtn.className = 'w-full py-3 px-4 bg-red-500 text-white rounded font-semibold buy-btn';
                availableLabel.textContent = '可用资金';
                availableAmount.textContent = formatAmount(accountData.availableFunds) + ' 元';
            } else {
                buyTab.className = 'flex-1 py-2 px-4 bg-gray-300 text-gray-700 rounded-l';
                sellTab.className = 'flex-1 py-2 px-4 bg-green-500 text-white rounded-r sell-btn';
                submitBtn.textContent = '卖出';
                submitBtn.className = 'w-full py-3 px-4 bg-green-500 text-white rounded font-semibold sell-btn';
                availableLabel.textContent = '可卖股数';

                // 查找持仓数量
                const position = positionData.find(p => p.stockCode === selectedTradingStock?.code);
                const availableShares = position ? position.availableQuantity : 0;
                availableAmount.textContent = availableShares.toLocaleString() + ' 股';
            }
        }

        function calculateAmount() {
            const price = parseFloat(document.getElementById('orderPrice').value) || 0;
            const quantity = parseInt(document.getElementById('orderQuantity').value) || 0;
            const amount = price * quantity;

            document.getElementById('orderAmount').textContent = formatAmount(amount) + ' 元';
        }

        function setPriceFromDepth(type) {
            if (!selectedTradingStock) return;

            const currentPrice = selectedTradingStock.currentPrice;
            let price;

            if (type === 'buy') {
                price = currentPrice - 0.01; // 买一价
            } else {
                price = currentPrice + 0.01; // 卖一价
            }

            document.getElementById('orderPrice').value = price.toFixed(2);
            calculateAmount();
        }

        function setPriceFromCurrent() {
            if (!selectedTradingStock) return;

            document.getElementById('orderPrice').value = selectedTradingStock.currentPrice.toFixed(2);
            calculateAmount();
        }

        function setQuantity(qty) {
            document.getElementById('orderQuantity').value = qty;
            calculateAmount();
        }

        function setMaxQuantity() {
            if (tradeDirection === 'buy') {
                const price = parseFloat(document.getElementById('orderPrice').value) || 0;
                if (price > 0) {
                    const maxQty = Math.floor(accountData.availableFunds / price / 100) * 100;
                    document.getElementById('orderQuantity').value = maxQty;
                }
            } else {
                const position = positionData.find(p => p.stockCode === selectedTradingStock?.code);
                if (position) {
                    document.getElementById('orderQuantity').value = position.availableQuantity;
                }
            }
            calculateAmount();
        }

        function submitOrder() {
            if (!selectedTradingStock) {
                showNotification('请先选择股票', 'error');
                return;
            }

            const orderType = document.querySelector('input[name="orderType"]:checked').value;
            const price = parseFloat(document.getElementById('orderPrice').value) || 0;
            const quantity = parseInt(document.getElementById('orderQuantity').value) || 0;
            const password = document.getElementById('tradePassword').value;

            // 验证输入
            if (orderType === 'limit' && price <= 0) {
                showNotification('请输入有效的委托价格', 'error');
                return;
            }

            if (quantity <= 0 || quantity % 100 !== 0) {
                showNotification('委托数量必须是100的整数倍', 'error');
                return;
            }

            if (!password) {
                showNotification('请输入交易密码', 'error');
                return;
            }

            // 显示确认对话框
            showConfirmModal({
                stockCode: selectedTradingStock.code,
                stockName: selectedTradingStock.name,
                direction: tradeDirection,
                orderType: orderType,
                price: orderType === 'limit' ? price : selectedTradingStock.currentPrice,
                quantity: quantity,
                amount: (orderType === 'limit' ? price : selectedTradingStock.currentPrice) * quantity
            });
        }

        function showConfirmModal(orderInfo) {
            const modal = document.getElementById('confirmModal');
            const content = document.getElementById('confirmContent');

            const directionText = orderInfo.direction === 'buy' ? '买入' : '卖出';
            const directionClass = orderInfo.direction === 'buy' ? 'text-red-500' : 'text-green-500';
            const typeText = orderInfo.orderType === 'limit' ? '限价单' : '市价单';

            content.innerHTML = `
                <div class="bg-gray-50 p-4 rounded">
                    <h4 class="font-semibold mb-3">请确认以下交易信息:</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>股票: <span class="font-semibold">${orderInfo.stockCode} ${orderInfo.stockName}</span></div>
                        <div>方向: <span class="font-semibold ${directionClass}">${directionText}</span></div>
                        <div>类型: <span class="font-semibold">${typeText}</span></div>
                        <div>价格: <span class="font-semibold">${formatNumber(orderInfo.price)} 元</span></div>
                        <div>数量: <span class="font-semibold">${orderInfo.quantity.toLocaleString()} 股</span></div>
                        <div>金额: <span class="font-semibold">${formatAmount(orderInfo.amount)} 元</span></div>
                    </div>
                </div>
                <div class="bg-yellow-50 border border-yellow-200 p-3 rounded mt-4">
                    <p class="text-yellow-800 text-sm">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        风险提示：股市有风险，投资需谨慎。请在充分了解相关风险的前提下进行投资决策。
                    </p>
                </div>
            `;

            modal.classList.remove('hidden');
            window.currentOrderInfo = orderInfo;
        }

        function hideConfirmModal() {
            document.getElementById('confirmModal').classList.add('hidden');
            window.currentOrderInfo = null;
        }

        function confirmTrade() {
            if (!window.currentOrderInfo) return;

            const orderInfo = window.currentOrderInfo;

            // 生成订单ID
            const orderId = 'ORD' + Date.now().toString().slice(-6);

            // 创建订单记录
            const newOrder = {
                id: orderId,
                stockCode: orderInfo.stockCode,
                stockName: orderInfo.stockName,
                direction: orderInfo.direction,
                price: orderInfo.price,
                quantity: orderInfo.quantity,
                status: 'pending',
                time: new Date().toLocaleTimeString()
            };

            orderData.push(newOrder);

            // 模拟订单处理
            setTimeout(() => {
                // 随机决定是否成交
                const isFilled = Math.random() > 0.3;

                if (isFilled) {
                    newOrder.status = 'filled';

                    // 更新持仓和资金
                    if (orderInfo.direction === 'buy') {
                        // 买入处理
                        accountData.availableFunds -= orderInfo.amount;

                        const existingPosition = positionData.find(p => p.stockCode === orderInfo.stockCode);
                        if (existingPosition) {
                            // 更新现有持仓
                            const totalCost = existingPosition.costValue + orderInfo.amount;
                            const totalQuantity = existingPosition.quantity + orderInfo.quantity;
                            existingPosition.avgCost = totalCost / totalQuantity;
                            existingPosition.quantity = totalQuantity;
                            existingPosition.availableQuantity = totalQuantity;
                            existingPosition.costValue = totalCost;
                            existingPosition.marketValue = totalQuantity * existingPosition.currentPrice;
                            existingPosition.pnlAmount = existingPosition.marketValue - existingPosition.costValue;
                            existingPosition.returnRate = (existingPosition.pnlAmount / existingPosition.costValue) * 100;
                        } else {
                            // 新建持仓
                            positionData.push({
                                stockCode: orderInfo.stockCode,
                                stockName: orderInfo.stockName,
                                quantity: orderInfo.quantity,
                                availableQuantity: orderInfo.quantity,
                                avgCost: orderInfo.price,
                                currentPrice: orderInfo.price,
                                marketValue: orderInfo.amount,
                                costValue: orderInfo.amount,
                                pnlAmount: 0,
                                returnRate: 0
                            });
                        }
                    } else {
                        // 卖出处理
                        accountData.availableFunds += orderInfo.amount;

                        const position = positionData.find(p => p.stockCode === orderInfo.stockCode);
                        if (position) {
                            position.quantity -= orderInfo.quantity;
                            position.availableQuantity -= orderInfo.quantity;
                            position.marketValue = position.quantity * position.currentPrice;
                            position.costValue = position.quantity * position.avgCost;
                            position.pnlAmount = position.marketValue - position.costValue;
                            position.returnRate = position.costValue > 0 ? (position.pnlAmount / position.costValue) * 100 : 0;

                            // 如果全部卖出，移除持仓
                            if (position.quantity === 0) {
                                positionData = positionData.filter(p => p.stockCode !== orderInfo.stockCode);
                            }
                        }
                    }

                    showNotification('订单已成交', 'success');
                } else {
                    showNotification('订单已提交，等待成交', 'info');
                }

                renderTodayOrders();
                renderPositions();
                updateAccountInfo();
            }, 1000);

            hideConfirmModal();

            // 清空表单
            document.getElementById('orderPrice').value = '';
            document.getElementById('orderQuantity').value = '';
            document.getElementById('tradePassword').value = '';
            document.getElementById('orderAmount').textContent = '0.00 元';

            showNotification('订单提交成功', 'success');
        }

        // 持仓相关函数
        function renderPositions() {
            const tbody = document.getElementById('positionTable');
            tbody.innerHTML = '';

            positionData.forEach(position => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                const pnlClass = position.pnlAmount >= 0 ? 'profit' : 'loss';
                const pnlPrefix = position.pnlAmount >= 0 ? '+' : '';

                row.innerHTML = `
                    <td class="px-4 py-3 stock-code">${position.stockCode}</td>
                    <td class="px-4 py-3">${position.stockName}</td>
                    <td class="px-4 py-3 text-right">${position.quantity.toLocaleString()}</td>
                    <td class="px-4 py-3 text-right">${position.availableQuantity.toLocaleString()}</td>
                    <td class="px-4 py-3 text-right">${formatNumber(position.avgCost)}</td>
                    <td class="px-4 py-3 text-right">${formatNumber(position.currentPrice)}</td>
                    <td class="px-4 py-3 text-right">${formatAmount(position.marketValue)}</td>
                    <td class="px-4 py-3 text-right ${pnlClass}">${pnlPrefix}${formatAmount(position.pnlAmount)}</td>
                    <td class="px-4 py-3 text-right ${pnlClass}">${pnlPrefix}${formatNumber(position.returnRate)}%</td>
                    <td class="px-4 py-3 text-center">
                        <button class="bg-green-500 text-white px-2 py-1 rounded text-xs mr-1 hover:bg-green-600"
                                onclick="sellPosition('${position.stockCode}')">卖出</button>
                        <button class="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600"
                                onclick="addPosition('${position.stockCode}')">加仓</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updatePositionSummary();
        }

        function updatePositionSummary() {
            const totalMarketValue = positionData.reduce((sum, p) => sum + p.marketValue, 0);
            const totalCost = positionData.reduce((sum, p) => sum + p.costValue, 0);
            const totalPnl = totalMarketValue - totalCost;
            const totalReturnRate = totalCost > 0 ? (totalPnl / totalCost) * 100 : 0;

            document.getElementById('totalMarketValue').textContent = formatAmount(totalMarketValue);
            document.getElementById('totalCost').textContent = formatAmount(totalCost);
            document.getElementById('totalPnl').textContent = (totalPnl >= 0 ? '+' : '') + formatAmount(totalPnl);
            document.getElementById('totalReturnRate').textContent = (totalReturnRate >= 0 ? '+' : '') + formatNumber(totalReturnRate) + '%';

            // 更新样式
            const pnlClass = totalPnl >= 0 ? 'profit' : 'loss';
            document.getElementById('totalPnl').className = `big-number ${pnlClass} amount-display`;
            document.getElementById('totalReturnRate').className = `big-number ${pnlClass}`;

            // 更新账户信息
            accountData.positionValue = totalMarketValue;
            accountData.totalAssets = accountData.availableFunds + totalMarketValue;
        }

        function filterPositions() {
            const searchTerm = document.getElementById('positionSearch').value.toLowerCase();
            const rows = document.querySelectorAll('#positionTable tr');

            rows.forEach(row => {
                const code = row.cells[0].textContent.toLowerCase();
                const name = row.cells[1].textContent.toLowerCase();
                if (code.includes(searchTerm) || name.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function refreshPositions() {
            // 更新持仓的当前价格
            positionData.forEach(position => {
                const stock = stockPoolData.find(s => s.code === position.stockCode);
                if (stock) {
                    position.currentPrice = stock.currentPrice;
                    position.marketValue = position.quantity * position.currentPrice;
                    position.pnlAmount = position.marketValue - position.costValue;
                    position.returnRate = position.costValue > 0 ? (position.pnlAmount / position.costValue) * 100 : 0;
                }
            });

            renderPositions();
            showNotification('持仓数据已刷新', 'success');
        }

        function sellPosition(stockCode) {
            switchTab('trading');
            document.getElementById('tradingStockSelect').value = stockCode;
            selectTradingStock();
            setTradeDirection('sell');
        }

        function addPosition(stockCode) {
            switchTab('trading');
            document.getElementById('tradingStockSelect').value = stockCode;
            selectTradingStock();
            setTradeDirection('buy');
        }

        // 委托订单相关函数
        function renderTodayOrders() {
            const container = document.getElementById('todayOrders');
            container.innerHTML = '';

            if (orderData.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-center py-4">暂无委托记录</div>';
                return;
            }

            orderData.slice(-5).reverse().forEach(order => {
                const div = document.createElement('div');
                div.className = 'border-b border-gray-200 pb-2 mb-2 last:border-b-0';

                const statusText = order.status === 'pending' ? '待成交' :
                                 order.status === 'filled' ? '已成交' : '已撤销';
                const statusClass = order.status === 'pending' ? 'text-yellow-600' :
                                  order.status === 'filled' ? 'text-green-600' : 'text-red-600';
                const directionClass = order.direction === 'buy' ? 'text-red-500' : 'text-green-500';
                const directionText = order.direction === 'buy' ? '买入' : '卖出';

                div.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div>
                            <div class="font-medium">${order.stockCode} ${order.stockName}</div>
                            <div class="text-xs text-gray-600">
                                <span class="${directionClass}">${directionText}</span>
                                ${formatNumber(order.price)} × ${order.quantity.toLocaleString()}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-xs ${statusClass}">${statusText}</div>
                            <div class="text-xs text-gray-600">${order.time}</div>
                        </div>
                    </div>
                    ${order.status === 'pending' ? `
                        <button class="text-xs bg-red-500 text-white px-2 py-1 rounded mt-2 hover:bg-red-600"
                                onclick="cancelOrder('${order.id}')">撤单</button>
                    ` : ''}
                `;
                container.appendChild(div);
            });
        }

        function cancelOrder(orderId) {
            if (confirm('确定要撤销这个委托吗？')) {
                const order = orderData.find(o => o.id === orderId);
                if (order) {
                    order.status = 'cancelled';
                    renderTodayOrders();
                    showNotification('委托已撤销', 'success');
                }
            }
        }

        // 账户信息更新
        function updateAccountInfo() {
            document.getElementById('totalAssets').textContent = formatAmount(accountData.totalAssets);
            document.getElementById('availableFunds').textContent = formatAmount(accountData.availableFunds);
            document.getElementById('positionValue').textContent = formatAmount(accountData.positionValue);
            document.getElementById('todayPnl').textContent = (accountData.todayPnl >= 0 ? '+' : '') + formatAmount(accountData.todayPnl);

            const pnlClass = accountData.todayPnl >= 0 ? 'profit' : 'loss';
            document.getElementById('todayPnl').className = `font-semibold ${pnlClass}`;
        }

        // 实时数据更新
        function updateRealtimeData() {
            // 模拟股价波动
            stockPoolData.forEach(stock => {
                const changeRate = (Math.random() - 0.5) * 0.02; // ±1%的随机波动
                const newPrice = stock.currentPrice * (1 + changeRate);

                stock.currentPrice = Math.max(0.01, newPrice);
                stock.changeAmount = stock.currentPrice - stock.prevClose;
                stock.changePercent = (stock.changeAmount / stock.prevClose) * 100;

                // 更新成交量（模拟增长）
                stock.volume += Math.floor(Math.random() * 100000);
                stock.turnover = stock.volume * stock.currentPrice;
            });

            // 更新持仓的当前价格和盈亏
            positionData.forEach(position => {
                const stock = stockPoolData.find(s => s.code === position.stockCode);
                if (stock) {
                    position.currentPrice = stock.currentPrice;
                    position.marketValue = position.quantity * position.currentPrice;
                    position.pnlAmount = position.marketValue - position.costValue;
                    position.returnRate = position.costValue > 0 ? (position.pnlAmount / position.costValue) * 100 : 0;
                }
            });

            // 重新渲染当前显示的数据
            if (currentTab === 'stockPool') {
                renderStockPool();
            } else if (currentTab === 'position') {
                renderPositions();
            } else if (currentTab === 'trading' && selectedTradingStock) {
                // 更新选中股票的信息
                const updatedStock = stockPoolData.find(s => s.code === selectedTradingStock.code);
                if (updatedStock) {
                    selectedTradingStock = updatedStock;
                    updateStockInfo();
                    updateDepthInfo();
                }
            }

            updateAccountInfo();
        }

        // 订单类型切换处理
        document.addEventListener('change', function(e) {
            if (e.target.name === 'orderType') {
                const priceField = document.getElementById('priceField');
                if (e.target.value === 'market') {
                    priceField.style.display = 'none';
                } else {
                    priceField.style.display = 'block';
                }
                calculateAmount();
            }
        });

        // 键盘事件处理
        document.addEventListener('keydown', function(e) {
            // ESC键关闭模态框
            if (e.key === 'Escape') {
                hideAddStockModal();
                hideConfirmModal();
            }
        });

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                hideAddStockModal();
                hideConfirmModal();
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeData();
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            setInterval(updateRealtimeData, 3000); // 每3秒更新一次实时数据
        });
    </script>
</body>
</html>
</body>
</html>
